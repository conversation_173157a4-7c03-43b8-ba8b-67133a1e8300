import { Box, Stack } from '@chakra-ui/react';
import dynamic from 'next/dynamic';
import ArticleTags from './article/article-tags';

const ArticleTitle = dynamic(() => import('./article/article-title'));
const ArticleMDX = dynamic(() => import('./article/article-viewer'));
const ArticleComment = dynamic(() => import('./article/article-comment'));

export default function LeftSide() {
  return (
    <Box className="col-span-2">
      <Stack gap={6}>
        <ArticleTitle />
        <Stack>
          <ArticleMDX />
          <ArticleTags />
        </Stack>
        <ArticleComment />
      </Stack>
    </Box>
  );
}
