'use client';

import React, { useState } from 'react';
import {
  Stack,
  Box,
  Text,
  Flex,
  Badge,
  IconButton,
  Menu,
  Image,
  AspectRatio,
  Card,
} from '@chakra-ui/react';
import {
  AiOutlineMore,
  AiOutlineEdit,
  AiOutlineDelete,
  AiOutlineSwap,
  AiOutlinePlayCircle,
} from 'react-icons/ai';
import { Video } from './types';
import VideoEditModal from './VideoEditModal';
import VideoMoveModal from './VideoMoveModal';
import VideoDeleteDialog from './VideoDeleteDialog';
import { toaster } from '@/components/ui/toaster';

// 组件 Props 类型
interface VideoListProps {
  folderId: string;
}

// 模拟视频数据
const mockVideos: Video[] = [
  {
    id: '1',
    title: 'React 基础教程',
    description: '从零开始学习 React 框架的基础知识',
    coverImage:
      'https://via.placeholder.com/320x180/4299e1/ffffff?text=React+Tutorial',
    duration: 1800, // 30分钟
    size: 256 * 1024 * 1024, // 256MB
    folderId: '2',
    status: 'published',
    createdAt: '2024-01-10T10:00:00Z',
  },
  {
    id: '2',
    title: 'JavaScript 进阶技巧',
    description: '深入了解 JavaScript 的高级特性和最佳实践',
    coverImage:
      'https://via.placeholder.com/320x180/f6ad55/ffffff?text=JavaScript+Advanced',
    duration: 2400, // 40分钟
    size: 512 * 1024 * 1024, // 512MB
    folderId: '2',
    status: 'draft',
    createdAt: '2024-01-12T14:30:00Z',
  },
  {
    id: '3',
    title: 'CSS 动画制作',
    description: '学习如何使用 CSS 创建流畅的动画效果',
    coverImage:
      'https://via.placeholder.com/320x180/10b981/ffffff?text=CSS+Animation',
    duration: 1200, // 20分钟
    size: 128 * 1024 * 1024, // 128MB
    folderId: '2',
    status: 'reviewing',
    createdAt: '2024-01-15T09:15:00Z',
  },
];

// 格式化时长
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  const mb = bytes / (1024 * 1024);
  return `${mb.toFixed(1)} MB`;
};

// 状态标签配置
const statusConfig = {
  draft: { label: '草稿', colorScheme: 'gray' },
  published: { label: '已发布', colorScheme: 'green' },
  reviewing: { label: '审核中', colorScheme: 'blue' },
  rejected: { label: '未通过', colorScheme: 'red' },
};

export default function VideoList({ folderId }: VideoListProps) {
  const [videos, setVideos] = useState<Video[]>(
    mockVideos.filter((video) => video.folderId === folderId)
  );
  const [editingVideo, setEditingVideo] = useState<Video | null>(null);
  const [movingVideo, setMovingVideo] = useState<Video | null>(null);
  const [deletingVideo, setDeletingVideo] = useState<Video | null>(null);

  // 处理编辑视频
  const handleEditVideo = (video: Video) => {
    setEditingVideo(video);
  };

  // 处理移动视频
  const handleMoveVideo = (video: Video) => {
    setMovingVideo(video);
  };

  // 处理删除视频
  const handleDeleteVideo = (video: Video) => {
    setDeletingVideo(video);
  };

  // 保存视频编辑
  const handleSaveVideo = (videoId: string, data: any) => {
    setVideos((prev) =>
      prev.map((video) =>
        video.id === videoId
          ? { ...video, ...data, updatedAt: new Date().toISOString() }
          : video
      )
    );
    setEditingVideo(null);
    toaster.create({
      title: '更新成功',
      description: '视频信息已更新',
      type: 'success',
      duration: 3000,
    });
  };

  // 确认移动视频
  const handleConfirmMove = (videoId: string, targetFolderId: string) => {
    setVideos((prev) => prev.filter((video) => video.id !== videoId));
    setMovingVideo(null);
    toaster.create({
      title: '移动成功',
      description: '视频已移动到目标文件夹',
      type: 'success',
      duration: 3000,
    });
  };

  // 确认删除视频
  const handleConfirmDelete = (videoId: string) => {
    setVideos((prev) => prev.filter((video) => video.id !== videoId));
    setDeletingVideo(null);
    toaster.create({
      title: '删除成功',
      description: '视频已删除',
      type: 'success',
      duration: 3000,
    });
  };

  if (videos.length === 0) {
    return (
      <Box textAlign="center" py={8}>
        <Text color="gray.500" fontSize="sm">
          此文件夹暂无视频
        </Text>
      </Box>
    );
  }

  return (
    <>
      <Stack gap={3}>
        {videos.map((video) => (
          <Card.Root key={video.id} variant="outline" size="sm">
            <Card.Body p={3}>
              <Flex gap={3}>
                {/* 视频封面 */}
                <Box flexShrink={0} width="120px" position="relative">
                  <AspectRatio ratio={16 / 9}>
                    <Image
                      src={video.coverImage}
                      alt={video.title}
                      borderRadius="md"
                      objectFit="cover"
                    />
                  </AspectRatio>
                  <Flex
                    position="absolute"
                    bottom={1}
                    right={1}
                    bg="blackAlpha.700"
                    color="white"
                    px={1}
                    py={0.5}
                    borderRadius="sm"
                    fontSize="xs"
                    align="center"
                    gap={1}
                  >
                    <AiOutlinePlayCircle size={10} />
                    <Text>{formatDuration(video.duration)}</Text>
                  </Flex>
                </Box>

                {/* 视频信息 */}
                <Box flex={1} minWidth={0}>
                  <Flex justify="space-between" align="flex-start" mb={2}>
                    <Box flex={1} minWidth={0}>
                      <Text
                        fontWeight="medium"
                        fontSize="sm"
                        lineClamp={1}
                        mb={1}
                      >
                        {video.title}
                      </Text>
                      <Text fontSize="xs" color="gray.600" lineClamp={2} mb={2}>
                        {video.description}
                      </Text>
                    </Box>

                    {/* 操作菜单 */}
                    <Menu.Root>
                      <Menu.Trigger asChild>
                        <IconButton
                          variant="ghost"
                          size="sm"
                          aria-label="更多操作"
                        >
                          <AiOutlineMore />
                        </IconButton>
                      </Menu.Trigger>
                      <Menu.Positioner>
                        <Menu.Content>
                          <Menu.Item
                            value="edit"
                            onClick={() => handleEditVideo(video)}
                          >
                            <AiOutlineEdit />
                            编辑
                          </Menu.Item>
                          <Menu.Item
                            value="move"
                            onClick={() => handleMoveVideo(video)}
                          >
                            <AiOutlineSwap />
                            移动
                          </Menu.Item>
                          <Menu.Item
                            value="delete"
                            onClick={() => handleDeleteVideo(video)}
                            color="fg.error"
                            _hover={{ bg: 'bg.error', color: 'fg.error' }}
                          >
                            <AiOutlineDelete />
                            删除
                          </Menu.Item>
                        </Menu.Content>
                      </Menu.Positioner>
                    </Menu.Root>
                  </Flex>

                  {/* 视频元信息 */}
                  <Flex
                    justify="space-between"
                    align="center"
                    fontSize="xs"
                    color="gray.500"
                  >
                    <Flex gap={3}>
                      <Text>{formatFileSize(video.size)}</Text>
                      <Text>
                        {new Date(video.createdAt).toLocaleDateString('zh-CN')}
                      </Text>
                    </Flex>
                    <Badge
                      colorScheme={statusConfig[video.status].colorScheme}
                      variant="subtle"
                      size="sm"
                    >
                      {statusConfig[video.status].label}
                    </Badge>
                  </Flex>
                </Box>
              </Flex>
            </Card.Body>
          </Card.Root>
        ))}
      </Stack>

      {/* 编辑视频模态框 */}
      <VideoEditModal
        isOpen={!!editingVideo}
        onClose={() => setEditingVideo(null)}
        video={editingVideo}
        onSave={handleSaveVideo}
      />

      {/* 移动视频模态框 */}
      <VideoMoveModal
        isOpen={!!movingVideo}
        onClose={() => setMovingVideo(null)}
        video={movingVideo}
        onConfirm={handleConfirmMove}
      />

      {/* 删除视频对话框 */}
      <VideoDeleteDialog
        isOpen={!!deletingVideo}
        onClose={() => setDeletingVideo(null)}
        video={deletingVideo}
        onConfirm={handleConfirmDelete}
      />
    </>
  );
}
