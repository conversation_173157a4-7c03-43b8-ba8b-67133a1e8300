'use client';

import {
  Box,
  VStack,
  Heading,
  Text,
  Avatar,
  IconButton,
  HStack,
  Card,
  Tag,
  Icon,
  useBreakpointValue,
  Container,
} from '@chakra-ui/react';
import { AiOutlinePlus, AiOutlineFire, AiOutlineUser } from 'react-icons/ai';

// 模拟的热门话题数据
const hotTopics = [
  { id: 1, name: '聚合物技术', count: 1280 },
  { id: 2, name: '催化剂研究', count: 865 },
  { id: 3, name: '工艺优化', count: 654 },
  { id: 4, name: '材料科学', count: 542 },
  { id: 5, name: '新能源应用', count: 421 },
];

// 模拟的推荐用户数据
const recommendedUsers = [
  {
    id: 1,
    name: '张教授',
    avatar: 'https://bit.ly/sage-adebayo',
    title: '高分子材料专家',
  },
  {
    id: 2,
    name: '李工程师',
    avatar: 'https://bit.ly/kent-c-dodds',
    title: '工艺设计师',
  },
  {
    id: 3,
    name: '王研究员',
    avatar: 'https://bit.ly/code-beast',
    title: '催化剂研究员',
  },
];

export default function RightSide() {
  // 在小屏幕上隐藏右侧边栏
  const isVisible = useBreakpointValue({ base: false, lg: true });

  if (!isVisible) return null;

  return (
    <Container
      position="sticky"
      top="4"
      width="100%"
      maxW="320px"
      py="4"
      px="2"
    >
      <VStack gap="6" align="stretch">
        {/* 热门话题卡片 */}
        <Card.Root
          borderWidth="1px"
          borderColor="border.default"
          borderRadius="md"
          overflow="hidden"
          bg="bg.card"
          boxShadow="sm"
        >
          <Card.Header pb="2">
            <HStack gap="2">
              <Icon as={AiOutlineFire} color="red.500" />
              <Heading size="sm">热门话题</Heading>
            </HStack>
          </Card.Header>
          <Card.Body pt="0">
            <VStack align="stretch" gap="3">
              {hotTopics.map((topic) => (
                <HStack key={topic.id} justify="space-between">
                  <Tag.Root
                    size="md"
                    variant="subtle"
                    bg="bg.subtle"
                    borderRadius="full"
                    px="3"
                  >
                    <Tag.Label>#{topic.name}</Tag.Label>
                  </Tag.Root>
                  <Text fontSize="xs" color="text.secondary">
                    {topic.count}讨论
                  </Text>
                </HStack>
              ))}
            </VStack>
          </Card.Body>
        </Card.Root>

        {/* 推荐用户卡片 */}
        <Card.Root
          borderWidth="1px"
          borderColor="border.default"
          borderRadius="md"
          overflow="hidden"
          bg="bg.card"
          boxShadow="sm"
        >
          <Card.Header pb="2">
            <HStack gap="2">
              <Tag.Root color="blue.500">
                <AiOutlineUser />
              </Tag.Root>
              <Heading size="sm">推荐关注</Heading>
            </HStack>
          </Card.Header>
          <Card.Body pt="0">
            <VStack align="stretch" gap="4">
              {recommendedUsers.map((user) => (
                <HStack key={user.id} justify="space-between">
                  <HStack gap="3">
                    <Avatar.Root size="sm" borderRadius="full">
                      <Avatar.Image src={user.avatar} alt={user.name} />
                      <Avatar.Fallback>{user.name[0]}</Avatar.Fallback>
                    </Avatar.Root>
                    <Box>
                      <Text fontWeight="medium" fontSize="sm">
                        {user.name}
                      </Text>
                      <Text fontSize="xs" color="text.secondary">
                        {user.title}
                      </Text>
                    </Box>
                  </HStack>
                  <IconButton
                    size="xs"
                    variant="outline"
                    colorScheme="blue"
                    borderRadius="full"
                  >
                    <AiOutlinePlus />
                    关注
                  </IconButton>
                </HStack>
              ))}
            </VStack>
          </Card.Body>
        </Card.Root>
        {/* 公告卡片 */}
        <Card.Root
          borderWidth="1px"
          borderColor="border.default"
          borderRadius="md"
          overflow="hidden"
          bg="bg.card"
          boxShadow="sm"
        >
          <Card.Header pb="2">
            <Heading size="sm">社区公告</Heading>
          </Card.Header>
          <Card.Body pt="0">
            <Text fontSize="sm">
              欢迎加入Steam聚合网站的交流社区！请遵守社区规则，文明发言，共同维护良好的讨论氛围。
            </Text>
            <Box as="hr" my="3" borderColor="border.default" />
            <Text fontSize="xs" color="text.secondary">
              更新于: 2023年10月15日
            </Text>
          </Card.Body>
        </Card.Root>
      </VStack>
    </Container>
  );
}
