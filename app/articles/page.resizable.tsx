import dynamic from 'next/dynamic';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import Content from '../_components/content';

const LeftSide = dynamic(() => import('./_components/left-side'));
const RightSide = dynamic(() => import('./_components/right-side'));
const ArticleTabs = dynamic(() => import('./_components/article-content'));

export default function Page() {
  return (
    <Content>
      <div style={{ height: '100%', width: '100%' }}>
        <PanelGroup direction="horizontal">
          <Panel
            defaultSize={20}
            minSize={10}
            maxSize={40}
            style={{ minWidth: 180 }}
          >
            <LeftSide />
          </Panel>
          <PanelResizeHandle
            style={{ width: 4, background: '#eee', cursor: 'col-resize' }}
          />
          <Panel defaultSize={60} minSize={30}>
            <ArticleTabs />
          </Panel>
          <PanelResizeHandle
            style={{ width: 4, background: '#eee', cursor: 'col-resize' }}
          />
          <Panel
            defaultSize={20}
            minSize={10}
            maxSize={40}
            style={{ minWidth: 220 }}
          >
            <RightSide />
          </Panel>
        </PanelGroup>
      </div>
    </Content>
  );
}
