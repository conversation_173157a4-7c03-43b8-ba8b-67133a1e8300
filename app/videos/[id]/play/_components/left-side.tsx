import dynamic from 'next/dynamic';
import { Box } from '@chakra-ui/react';
import VideoTitle from './video/video-title';
import VideoAction from './video/video-action';
import VideoDescribe from './video/video-describe';

const VideoPlayer = dynamic(() => import('./video/video-player'));

export default function VideoPlay() {
  /**
   * @description 视频播放页面
   */
  return (
    <Box className="col-span-2 flex flex-col gap-2">
      <VideoTitle />
      <VideoPlayer />
      <VideoAction />
      <VideoDescribe />
      <Box>评论组件</Box>
    </Box>
  );
}
