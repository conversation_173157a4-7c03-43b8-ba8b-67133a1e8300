import { Container, Flex, Tabs, Box, Stack } from '@chakra-ui/react';

import dynamic from 'next/dynamic';
import { AiFillMessage, AiFillEdit } from 'react-icons/ai';
import SettingsContainer from './_components/profile-container';

const ProfileMessage = dynamic(() => import('./_components/profile-message'));
const PostCard = dynamic(() => import('./_components/posts/post-card'));
const EditAvatar = dynamic(() => import('./_components/edit/edit-avatar'));
const EditProfile = dynamic(() => import('./_components/edit/edit-profile'));

export default function Page() {
  /**
   * @description 个人资料展示页面
   */
  return (
    <Container maxW="6xl" h={'100%'} p={0}>
      <Flex direction={'column'} gap={2}>
        <ProfileMessage />
        <Tabs.Root defaultValue="posts" variant={'line'} bg={'white'}>
          <Tabs.List>
            <Tabs.Trigger value="posts">
              <AiFillMessage />
              简聊
            </Tabs.Trigger>
            <Tabs.Trigger value="edit">
              <AiFillEdit />
              编辑
            </Tabs.Trigger>
            <Tabs.Trigger value="articles">
              <AiFillMessage />
              我的文章
            </Tabs.Trigger>
            <Tabs.Trigger value="videos">
              <AiFillMessage />
              我的视频
            </Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content
            value="posts"
            inset="0"
            _open={{
              animationName: 'fade-in, scale-in',
              animationDuration: '300ms',
            }}
            _closed={{
              animationName: 'fade-out, scale-out',
              animationDuration: '120ms',
            }}
            borderWidth={'0px'}
            p={0}
          >
            <SettingsContainer className={'flex flex-col gap-4 py-2'}>
              <PostCard />
            </SettingsContainer>
          </Tabs.Content>
          <Tabs.Content
            value="edit"
            inset="0"
            _open={{
              animationName: 'fade-in, scale-in',
              animationDuration: '300ms',
            }}
            _closed={{
              animationName: 'fade-out, scale-out',
              animationDuration: '120ms',
            }}
            borderWidth={'0px'}
            p={0}
          >
            <SettingsContainer className={'grid grid-cols-6 gap-4'}>
              <Box className="col-span-2">
                <EditAvatar />
              </Box>
              <Box className="col-span-4">
                <EditProfile />
              </Box>
            </SettingsContainer>
          </Tabs.Content>
          <Tabs.Content
            value="articles"
            inset="0"
            _open={{
              animationName: 'fade-in, scale-in',
              animationDuration: '300ms',
            }}
            _closed={{
              animationName: 'fade-out, scale-out',
              animationDuration: '120ms',
            }}
            borderWidth={'0px'}
            p={0}
          >
            我的文章
          </Tabs.Content>
          <Tabs.Content
            value="videos"
            inset="0"
            _open={{
              animationName: 'fade-in, scale-in',
              animationDuration: '300ms',
            }}
            _closed={{
              animationName: 'fade-out, scale-out',
              animationDuration: '120ms',
            }}
            borderWidth={'0px'}
            p={0}
          >
            我的视频
          </Tabs.Content>
        </Tabs.Root>
      </Flex>
    </Container>
  );
}
