import { createContext, useContext } from 'react';
import { Box } from '@chakra-ui/react';
import { AnimatePresence, motion } from 'framer-motion';
import {
  SmoothMenuProps,
  MenuContextType,
  MenuItem as MenuItemType,
} from './types';
import { useMenu } from './useMenu';
import { MenuItem } from './MenuItem';
import { SubMenu } from './SubMenu';

// 创建菜单上下文
const MenuContext = createContext<MenuContextType | null>(null);

// 使用菜单上下文的钩子
export const useMenuContext = () => {
  const context = useContext(MenuContext);
  if (!context) {
    throw new Error('useMenuContext must be used within a SmoothMenu');
  }
  return context;
};

const MotionBox = motion(Box);

export const SmoothMenu = ({
  items,
  mode = 'vertical',
  defaultSelectedKeys = [],
  defaultOpenKeys = [],
  onSelect: onSelectProp,
  style,
}: SmoothMenuProps) => {
  // 使用菜单钩子管理状态
  const { selectedKeys, openKeys, onSelect, onOpenChange } = useMenu({
    defaultSelectedKeys,
    defaultOpenKeys,
    onSelect: onSelectProp,
  });

  // 渲染菜单项
  const renderMenuItem = (item: MenuItemType) => {
    if (item.children && item.children.length > 0) {
      return (
        <SubMenu
          key={item.key}
          item={item}
          isOpen={openKeys.includes(item.key)}
          selectedKeys={selectedKeys}
          onSelect={onSelect}
          onOpenChange={onOpenChange}
        />
      );
    }

    return (
      <MenuItem
        key={item.key}
        item={item}
        isSelected={selectedKeys.includes(item.key)}
        onSelect={onSelect}
      />
    );
  };

  return (
    <MenuContext.Provider
      value={{
        selectedKeys,
        openKeys,
        onSelect,
        onOpenChange,
      }}
    >
      <Box as="nav" style={style} bg="white" _dark={{ bg: 'gray.800' }}>
        <MotionBox
          as="ul"
          role="menu"
          display="flex"
          flexDirection={mode === 'vertical' ? 'column' : 'row'}
          p={0}
          m={0}
          listStyleType="none"
        >
          <AnimatePresence initial={false}>
            {items.map(renderMenuItem)}
          </AnimatePresence>
        </MotionBox>
      </Box>
    </MenuContext.Provider>
  );
};
