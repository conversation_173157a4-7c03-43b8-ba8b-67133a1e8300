'use client';
import {
  Box,
  AspectRatio,
  Image,
  HStack,
  Avatar,
  Stack,
  Text,
  IconButton,
} from '@chakra-ui/react';
import NextImage from 'next/image';
import CoverBackground from '@/public/images/cover.jpg';
import { AiFillCamera } from 'react-icons/ai';

export default function ProfileMessage() {
  /**
   * @description 用户封面 头像信息展示
   */
  return (
    <Box className="relative" maxH={96} overflow={'hidden'}>
      <AspectRatio ratio={16 / 4}>
        <Image asChild>
          <NextImage
            src={CoverBackground}
            alt="cover"
            layout="fill"
            className="bg-background rounded-t-md"
          />
        </Image>
      </AspectRatio>
      <Box
        className="absolute bottom-0 left-0 flex w-full -translate-y-1 items-center justify-between text-white"
        px={4}
        maxH={32}
      >
        <HStack gap="3" style={{ marginBottom: 0 }}>
          <Avatar.Root size={'xl'}>
            <Avatar.Image src="https://images.unsplash.com/photo-1511806754518-53bada35f930" />
            <Avatar.Fallback name="<PERSON> Foss" />
          </Avatar.Root>
          <Stack gap="0" mb={0}>
            <Text
              fontWeight="semibold"
              textStyle="md"
              className="tracking-wide"
            >
              Nate Foss
            </Text>
            <Text textStyle="sm">@natefoss</Text>
          </Stack>
        </HStack>
        <IconButton bg={'white'} variant={'plain'} color={'gray.500'} px={4}>
          <AiFillCamera />
          编辑封面
        </IconButton>
      </Box>
    </Box>
  );
}
