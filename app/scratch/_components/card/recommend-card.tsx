'use client';
import { Box, Card, Image, Text } from '@chakra-ui/react';
import NextImage from 'next/image';
import { AiOutlineUser } from 'react-icons/ai';

import Paint from '@/public/images/paint.svg';

export default function RecommendCard() {
  return (
    <Card.Root className="group h-40" overflow={'hidden'} variant={'elevated'}>
      <Card.Body p={0} className="relative">
        <Image asChild>
          <NextImage src={Paint} alt={'paint'} layout={'fill'} />
        </Image>
        <Box
          w={'100%'}
          className="absolute bottom-0 left-0 flex h-10 items-center rounded-md bg-gray-100 opacity-70 group-hover:invisible"
        >
          <Box px={2} className="flex w-full justify-between">
            <Text maxW={'50%'} truncate className="!text-sm !font-semibold">
              推荐作品名称
            </Text>
            <Text
              maxW={'50%'}
              truncate
              className="flex items-center gap-2 !text-sm !font-semibold"
            >
              <AiOutlineUser className="mr-2 inline-block" />
              用户名称
            </Text>
          </Box>
        </Box>
      </Card.Body>
    </Card.Root>
  );
}
