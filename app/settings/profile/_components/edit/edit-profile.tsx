import { Card } from '@chakra-ui/react';
import EditForm from './edit-form';

/**
 * 编辑个人资料卡片组件
 *
 * 提供一个包含编辑表单的卡片容器，采用大尺寸和提升样式变体
 * 内部使用 EditForm 组件处理具体编辑逻辑
 */
export default function EditProfile() {
  return (
    <Card.Root
      variant={'elevated'}
      size={'lg'}
      w={'100%'}
      className="flex flex-col gap-4"
    >
      <Card.Body>
        <EditForm />
      </Card.Body>
    </Card.Root>
  );
}
