import { Box, Stack } from '@chakra-ui/react';
import dynamic from 'next/dynamic';
import ArticleAction from './article/article-action';

const UserCard = dynamic(() => import('@/app/_components/user/user-card'));
const ArticleRecommend = dynamic(() => import('./article/article-recommend'));

/**
 * 文章详情页右侧边栏组件
 * 包含用户卡片、文章操作按钮和推荐文章列表
 * @returns 右侧边栏布局
 */
export default function RightSide() {
  return (
    <Box className="col-span-1">
      <Box mt={4}>
        <Stack gap={6}>
          <UserCard />
          <ArticleAction />
          <ArticleRecommend />
        </Stack>
      </Box>
    </Box>
  );
}
