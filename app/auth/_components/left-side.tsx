'use client';
import {
  Container,
  Box,
  Heading,
  Image,
  AspectRatio,
  Group,
  IconButton,
} from '@chakra-ui/react';
import NextImage from 'next/image';
import { Typewriter } from './type-writer';
import AuthImage from '@/public/images/auth.webp';

import { SiScratch } from 'react-icons/si';
import { AiOutlineVideoCamera, AiOutlineEdit } from 'react-icons/ai';

export default function LeftSide() {
  return (
    <Container fluid className="flex h-full flex-col gap-6 !pt-10 !pb-5">
      <Box className="flex h-15 flex-col items-center gap-6">
        <Heading as="h1" size="2xl" className="text-center select-none">
          极致创思聚合网站
        </Heading>
        <Typewriter
          text="编程、文章、视频等多种资源聚合"
          delay={100}
          cursorColor="white"
          loop={true}
        />
      </Box>

      <Box className="!mt-auto">
        <AspectRatio maxW="400px" ratio={4 / 3}>
          <Image asChild>
            <NextImage
              src={AuthImage}
              alt="auth-image"
              layout="fill"
              className="rounded-lg shadow-md select-none"
            />
          </Image>
        </AspectRatio>
      </Box>

      <Box className="!my-auto flex w-full justify-end">
        <Group>
          <IconButton variant="plain">
            <SiScratch />
          </IconButton>
          <IconButton variant="plain">
            <AiOutlineVideoCamera />
          </IconButton>
          <IconButton variant="plain">
            <AiOutlineEdit />
          </IconButton>
        </Group>
      </Box>
    </Container>
  );
}
