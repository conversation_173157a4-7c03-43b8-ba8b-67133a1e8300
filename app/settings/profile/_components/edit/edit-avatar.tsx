'use client';
import {
  Card,
  Avatar,
  Box,
  defineStyle,
  Stack,
  Text,
  Button,
} from '@chakra-ui/react';
import { AiOutlineCamera } from 'react-icons/ai';

const ringCss = defineStyle({
  outlineWidth: '1px',
  outlineColor: 'colorPalette.500',
  outlineOffset: '2px',
  outlineStyle: 'dashed',
});

/**
 * 头像编辑组件
 *
 * 提供一个可点击的卡片区域用于上传或修改用户头像
 * 包含头像预览、上传按钮和格式提示
 * 支持鼠标悬停效果：头像变暗并显示相机图标按钮
 *
 * @returns 返回一个包含头像编辑界面的卡片组件
 */
export default function EditAvatar() {
  return (
    <Card.Root h={80} variant={'elevated'} size={'lg'} w={'100%'}>
      <Card.Body className="flex flex-col">
        <Box className="flex h-1/2 items-center justify-center">
          <Stack gap={3}>
            <Box className="group relative flex justify-center">
              <Avatar.Root
                h={'89px'}
                w={'89px'}
                css={ringCss}
                className="relative group-hover:brightness-50"
              >
                <Avatar.Image src="https://images.unsplash.com/photo-1511806754518-53bada35f930" />
                <Avatar.Fallback name="Nate Foss" />
              </Avatar.Root>
              <Box className="bg-colorPalette.500 left-50% top-50% absolute translate-y-[50%] rounded-full opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                <Button variant={'plain'} size={'lg'} color={'white'}>
                  <AiOutlineCamera />
                </Button>
              </Box>
            </Box>

            <Text color={'fg.muted'} textStyle="sm" textAlign={'center'}>
              Allowed *.jpeg, *.jpg, *.png, *.gif max size of 3 Mb
            </Text>
          </Stack>
        </Box>
      </Card.Body>
    </Card.Root>
  );
}
