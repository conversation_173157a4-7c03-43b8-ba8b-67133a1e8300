@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* 语义化颜色变量 - 亮色模式 */
  --color-bg: #ffffff;
  --color-bg-subtle: #f5f5f5;
  --color-bg-muted: #f0f0f0;
  --color-bg-emphasized: #e5e5e5;

  --color-text: #171717;
  --color-text-muted: #666666;
  --color-text-subtle: #8f8f8f;
  --color-text-emphasized: #000000;

  --color-border: #e2e2e2;
  --color-border-subtle: #ebebeb;
  --color-border-emphasized: #d1d1d1;

  --color-card-bg: #ffffff;
  --color-card-border: #e2e2e2;

  --color-button-bg: #f5f5f5;
  --color-button-hover: #e5e5e5;
  --color-button-active: #d4d4d4;

  --color-sky-50: oklch(0.977 0.013 236.62);
  --color-sky-100: oklch(0.951 0.026 236.824);
  --color-sky-200: oklch(0.901 0.058 230.902);
  --color-sky-300: oklch(0.828 0.111 230.318);
  --color-sky-400: oklch(0.746 0.16 232.661);
  --color-sky-500: oklch(0.685 0.169 237.323);
  --color-sky-600: oklch(0.588 0.158 241.966);
  --color-sky-700: oklch(0.5 0.134 242.749);
  --color-sky-800: oklch(0.443 0.11 240.79);
  --color-sky-900: oklch(0.391 0.09 240.876);
  --color-sky-950: oklch(0.293 0.066 243.157);

  --color-gray-50: oklch(0.984 0.003 247.858);
  --color-gray-100: oklch(0.968 0.007 247.896);
  --color-gray-200: oklch(0.929 0.013 255.508);
  --color-gray-300: oklch(0.869 0.022 252.894);
  --color-gray-400: oklch(0.704 0.04 256.788);
  --color-gray-500: oklch(0.554 0.046 257.417);
  --color-gray-600: oklch(0.446 0.043 257.281);
  --color-gray-700: oklch(0.372 0.044 257.287);
  --color-gray-800: oklch(0.279 0.041 260.031);
  --color-gray-900: oklch(0.208 0.042 265.755);
  --color-gray-950: oklch(0.129 0.042 264.695);

  --color-blue-50: oklch(0.97 0.014 254.604);
  --color-blue-100: oklch(0.932 0.032 255.585);
  --color-blue-200: oklch(0.882 0.059 254.128);
  --color-blue-300: oklch(0.809 0.105 251.813);
  --color-blue-400: oklch(0.707 0.165 254.624);
  --color-blue-500: oklch(0.623 0.214 259.815);
  --color-blue-600: oklch(0.546 0.245 262.881);
  --color-blue-700: oklch(0.488 0.243 264.376);
  --color-blue-800: oklch(0.424 0.199 265.638);
  --color-blue-900: oklch(0.379 0.146 265.522);
  --color-blue-950: oklch(0.282 0.091 267.935);

  --color-violet-50: oklch(0.969 0.016 293.756);
  --color-violet-100: oklch(0.943 0.029 294.588);
  --color-violet-200: oklch(0.894 0.057 293.283);
  --color-violet-300: oklch(0.811 0.111 293.571);
  --color-violet-400: oklch(0.702 0.183 293.541);
  --color-violet-500: oklch(0.606 0.25 292.717);
  --color-violet-600: oklch(0.541 0.281 293.009);
  --color-violet-700: oklch(0.491 0.27 292.581);
  --color-violet-800: oklch(0.432 0.232 292.759);
  --color-violet-900: oklch(0.38 0.189 293.745);
  --color-violet-950: oklch(0.283 0.141 291.089);

  --header-height: 64px;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--color-blue-400);
  --color-primary-50: var(--color-blue-50);
  --color-primary-100: var(--color-blue-100);
  --color-primary-200: var(--color-blue-200);
  --color-primary-300: var(--color-blue-300);
  --color-primary-500: var(--color-blue-500);
  --color-primary-600: var(--color-blue-600);

  --color-secondary: var(--color-violet-400);
  --color-secondary-50: var(--color-violet-50);
  --color-secondary-100: var(--color-violet-100);
  --color-secondary-200: var(--color-violet-200);
  --color-secondary-300: var(--color-violet-300);
  --color-secondary-500: var(--color-violet-500);
  --color-secondary-600: var(--color-violet-600);

  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-noto: var(--font-noto-sans-sc);

  --height-header: var(--header-height);

  --animate-fade-in-scale: fade-in-scale 0.3s ease-out;

  @keyframes fade-in-scale {
    0% {
      opacity: 0;
      transform: scale(0.95);
    }

    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }


}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;

    /* 语义化颜色变量 - 暗色模式 */
    --color-bg: #0a0a0a;
    --color-bg-subtle: #1a1a1a;
    --color-bg-muted: #262626;
    --color-bg-emphasized: #333333;

    --color-text: #ededed;
    --color-text-muted: #a3a3a3;
    --color-text-subtle: #737373;
    --color-text-emphasized: #ffffff;

    --color-border: #333333;
    --color-border-subtle: #262626;
    --color-border-emphasized: #404040;

    --color-card-bg: #1a1a1a;
    --color-card-border: #333333;

    --color-button-bg: #262626;
    --color-button-hover: #333333;
    --color-button-active: #404040;
  }
}

/* 手动切换暗色模式的类 */
.dark {
  --background: #0a0a0a;
  --foreground: #ededed;

  /* 语义化颜色变量 - 暗色模式 */
  --color-bg: #0a0a0a;
  --color-bg-subtle: #1a1a1a;
  --color-bg-muted: #262626;
  --color-bg-emphasized: #333333;

  --color-text: #ededed;
  --color-text-muted: #a3a3a3;
  --color-text-subtle: #737373;
  --color-text-emphasized: #ffffff;

  --color-border: #333333;
  --color-border-subtle: #262626;
  --color-border-emphasized: #404040;

  --color-card-bg: #1a1a1a;
  --color-card-border: #333333;

  --color-button-bg: #262626;
  --color-button-hover: #333333;
  --color-button-active: #404040;
}

@layer components {
  .active {
    color: var(--color-sky-500) !important;
  }

  .scroll-hover-hide {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }

  .scroll-hover-hide:hover {
    scrollbar-width: thin !important;
    -ms-overflow-style: auto !important;
  }

  .md-editor {
    height: 100%;
    width: 100%;

    &>div {
      height: 100% !important;
    }

    .bytemd-toolbar-icon.bytemd-tippy.bytemd-tippy-right:last-child {
      display: none !important;
    }

    .bytemd {
      height: 100% !important;
      border-radius: 10px;

      .bytemd-toolbar {
        border-radius: 10px;
      }
    }
  }

}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-noto-sans-sc) !important;
}