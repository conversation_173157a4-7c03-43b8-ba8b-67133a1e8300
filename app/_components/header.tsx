'use client';
import { useMemo, useState, useCallback } from 'react';
import dynamic from 'next/dynamic';
import { Box, Menu, Portal, IconButton } from '@chakra-ui/react';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import Link from 'next/link';
import { FaRegUserCircle } from 'react-icons/fa';
import { GoSignOut } from 'react-icons/go';
import { AiOutlineSend } from 'react-icons/ai';

import Logo from '@/public/images/logo.png';
import IconGroup from './header/icon-group';
import SearchInput from './header/search-input';
import { useArticleCreateStore } from '@/store/article-create-store';
import useNotificationStore from '@store/notification-store';

const UserAvatar = dynamic(() => import('./header/avatar'), {
  ssr: false,
});

export default function SteamHeader() {
  /**
   * @description steam 聚合网站 header组件
   */
  const pathname = usePathname();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const show = useArticleCreateStore((state) => state.show);
  const handleDrawerShow = useArticleCreateStore(
    (state) => state.handleDrawerShow
  );
  const notificationState = useNotificationStore((state) => state.open);
  const handleNotificationState = useNotificationStore(
    (state) => state.handleNotificationState
  );

  /**
   * 判断当前路径是否激活
   * @param path 要匹配的路径
   * @returns 返回布尔值，表示当前路径是否匹配或包含给定路径
   * @description
   * - 当path为'/'时，严格匹配根路径
   * - 其他情况下匹配路径或子路径（path/*）
   * @memoized 依赖pathname变化时重新计算
   */
  const isPathActivate = useMemo(
    () => (path: string) =>
      path === '/'
        ? pathname === '/'
        : pathname === path || pathname.startsWith(`${path}/`),
    [pathname]
  );

  /**
   * @description 控制创建文章drawer显示和隐藏
   */
  const handleDrawerOpen = () => {
    handleDrawerShow();
  };

  const handleMenuState = useCallback(() => setOpen(!open), [open]);

  const handleNavigation = (path: string) => {
    router.push(path);
  };
  return (
    <Box
      p={4}
      className="bg-background h-header flex items-center justify-between gap-3 rounded-lg"
      borderBottomWidth="1px"
    >
      <div
        className="flex cursor-pointer items-center"
        onClick={() => handleNavigation('/')}
      >
        <Image
          src={Logo}
          alt="logo"
          priority
          placeholder="blur"
          width={135}
          height={65}
        />
      </div>
      <div className="flex flex-1 gap-3">
        <Link
          href="/"
          className={`font-semibold ${isPathActivate('/') ? 'active' : ''}`}
        >
          首页
        </Link>
        <Link
          href="/articles"
          className={`font-semibold ${isPathActivate('/articles') ? 'active' : ''}`}
        >
          文章
        </Link>
        <Link
          href="/videos"
          className={`font-semibold ${isPathActivate('/videos') ? 'active' : ''}`}
        >
          视频
        </Link>
        <Link
          href="/posts"
          className={`font-semibold ${isPathActivate('/posts') ? 'active' : ''}`}
        >
          简聊
        </Link>
        <Link
          href="/scratch"
          className={`font-semibold ${isPathActivate('/scratch') ? 'active' : ''}`}
        >
          scratch 编程
        </Link>
      </div>
      <Box
        className="relative flex flex-1 items-center justify-end gap-3"
        paddingRight={4}
      >
        {show && (
          <IconButton
            size="xs"
            bg="primary"
            color={'white'}
            px={2}
            onClick={handleDrawerOpen}
          >
            <AiOutlineSend />
            发布文章
          </IconButton>
        )}

        <SearchInput />
        <IconGroup
          notificationState={notificationState}
          handleNotificationState={handleNotificationState}
          handleNavigation={handleNavigation}
        />
        <Menu.Root positioning={{ placement: 'bottom-end' }} open={open}>
          <Menu.Trigger>
            <UserAvatar handleMenuState={handleMenuState} />
          </Menu.Trigger>
          <Portal>
            <Menu.Positioner>
              <Menu.Content>
                <Menu.Item value="profile">
                  <FaRegUserCircle />
                  账户管理
                </Menu.Item>
                <Menu.Item value="signout">
                  <GoSignOut />
                  退出登录
                </Menu.Item>
              </Menu.Content>
            </Menu.Positioner>
          </Portal>
        </Menu.Root>
      </Box>
    </Box>
  );
}
