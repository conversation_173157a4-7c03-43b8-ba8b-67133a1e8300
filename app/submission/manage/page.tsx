import { Flex, Tabs } from '@chakra-ui/react';
import { AiOutlineCheck, AiOutlineClose } from 'react-icons/ai';
import dynamic from 'next/dynamic';
import { RiDraftLine } from 'react-icons/ri';

const ExamineView = dynamic(() => import('./_components/examine-view'));

export default function Page() {
  /**
   * @description 草稿箱
   */
  return (
    <Flex gap={4} direction={'column'} h={'100%'}>
      <Tabs.Root defaultValue="members" variant={'outline'}>
        <Tabs.List>
          <Tabs.Trigger value="members">
            <RiDraftLine />
            我的草稿
          </Tabs.Trigger>
          <Tabs.Trigger value="projects">
            <AiOutlineCheck />
            审核中
          </Tabs.Trigger>
          <Tabs.Trigger value="tasks">
            <AiOutlineClose />
            审核未通过
          </Tabs.Trigger>
        </Tabs.List>
        <Tabs.Content value="members">
          <ExamineView />
        </Tabs.Content>
        <Tabs.Content value="projects">
          <ExamineView />
        </Tabs.Content>
        <Tabs.Content value="tasks">
          <ExamineView />
        </Tabs.Content>
      </Tabs.Root>
    </Flex>
  );
}
