import { Box, VStack } from '@chakra-ui/react';
import dynamic from 'next/dynamic';
import PostsEdit from './posts/posts-edit';

const PostsList = dynamic(() => import('./posts/posts-list'));

export default function PostsContent() {
  return (
    <Box width="100%" maxW="800px" mx="auto" px={{ base: '4', md: '6' }} py="6">
      <VStack gap="6" align="stretch">
        <PostsEdit />
        {/* 帖子列表组件 */}
        <PostsList />
      </VStack>
    </Box>
  );
}
