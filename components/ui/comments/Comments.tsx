import React from 'react';
import { Box, Heading, Separator } from '@chakra-ui/react';
import { useColorModeValue } from '@/components/ui/color-mode';
import { CommentsProps } from './types';
import CommentInput from './CommentInput';
import CommentList from './CommentList';

export const Comments: React.FC<CommentsProps> = ({
  comments,
  currentUser,
  onAddComment,
  onReply,
  onLike,
  onEdit,
  onDelete,
  onLoadMore,
  hasMore,
  isLoading,
}) => {
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <Box>
      <Heading as="h3" size="md" mb={4}>
        评论
      </Heading>

      {currentUser && (
        <Box mb={6}>
          <CommentInput
            onSubmit={onAddComment}
            placeholder="写下你的评论..."
            isLoading={isLoading}
          />
        </Box>
      )}

      <Separator my={6} borderColor={borderColor} />

      <CommentList
        comments={comments}
        onLoadMore={onLoadMore}
        hasMore={hasMore}
        isLoading={isLoading}
        onReply={onReply}
        onLike={onLike}
        onEdit={onEdit}
        onDelete={onDelete}
        currentUser={currentUser}
      />
    </Box>
  );
};

export default Comments;
