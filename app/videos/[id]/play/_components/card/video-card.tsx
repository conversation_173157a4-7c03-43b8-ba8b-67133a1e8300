'use client';
import { Card, Stack, Badge, Image, Box, Text } from '@chakra-ui/react';
import NextImage from 'next/image';
import { AiOutlineUser, AiFillEye, AiOutlineComment } from 'react-icons/ai';
import Paint from '@/public/images/paint.svg';

export default function VideoCard() {
  /**
   * @description 推荐视频 card
   */
  return (
    <Card.Root borderWidth={'0px'} cursor={'pointer'} className="group">
      <Card.Body padding={'0px'}>
        <Stack direction="row" gap={2}>
          <Box className="w-1/2 rounded-lg" border={'1px solid gray.200'}>
            <Image asChild height={'100%'} width="100%">
              <NextImage src={Paint} alt="paint" width={150} height={100} />
            </Image>
          </Box>
          <Stack direction="column" gap={2}>
            <Text
              lineClamp={2}
              textStyle={'md'}
              className="group-hover:!text-primary !font-semibold tracking-wide"
            >
              标题
            </Text>
            <Badge variant="plain" px="0" size="md" color="gray.500">
              <AiOutlineUser />
              用户名username
            </Badge>
            <Stack direction="row" gap="2">
              <Badge variant="plain" px="0" size="md" color="gray.500">
                <AiFillEye />
                5.2k
              </Badge>
              <Badge variant="plain" px="0" size="md" color="gray.500">
                <AiOutlineComment />
                5.2k
              </Badge>
            </Stack>
          </Stack>
        </Stack>
      </Card.Body>
    </Card.Root>
  );
}
