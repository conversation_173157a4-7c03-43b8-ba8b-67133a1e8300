'use client';
import {
  Card,
  Button,
  Image,
  AspectRatio,
  Badge,
  Flex,
  Box,
} from '@chakra-ui/react';
import NextImage from 'next/image';
import Paint from '@/public/images/paint.svg';
import React from 'react';

export type VideoStatus = '待审核' | '已通过' | '未通过';

interface VideoCardWithStatusProps {
  title: string;
  description: string;
  status: VideoStatus;
  cover?: string;
  reason?: string;
}

const statusColor: Record<VideoStatus, string> = {
  待审核: 'yellow',
  已通过: 'green',
  未通过: 'red',
};

export default function VideoCardWithStatus({
  title,
  description,
  status,
  cover,
  reason,
}: VideoCardWithStatusProps) {
  return (
    <Card.Root
      maxW="lg"
      overflow="hidden"
      className="transition-all hover:shadow-lg"
    >
      <Flex direction={{ base: 'column', md: 'row' }} alignItems={'center'}>
        <Box flexShrink={0} w={{ base: '100%', md: '220px' }}>
          <AspectRatio ratio={16 / 9} w="full">
            <Image asChild>
              <NextImage src={cover || Paint} alt={title} layout="fill" />
            </Image>
          </AspectRatio>
        </Box>
        <Box
          flex="1"
          p={4}
          display="flex"
          flexDirection="column"
          justifyContent="space-between"
        >
          <Card.Body gap="2" p={0}>
            <Flex align="center" justify="space-between" mb={2}>
              <Card.Title>{title}</Card.Title>
              <Badge colorScheme={statusColor[status]} fontSize="0.8em">
                {status}
              </Badge>
            </Flex>
            <Card.Description className="line-clamp-2" mb={4}>
              {description}
            </Card.Description>
            {status === '未通过' && reason && (
              <Box color="red.500" fontSize="sm" mt={1}>
                审核未通过原因：{reason}
              </Box>
            )}
          </Card.Body>
          <Card.Footer gap="2" className="flex justify-end" p={0} mt={2}>
            <Button size={'xs'} variant="solid">
              编辑
            </Button>
            <Button colorPalette="red" size={'xs'} variant="ghost">
              删除
            </Button>
          </Card.Footer>
        </Box>
      </Flex>
    </Card.Root>
  );
}
