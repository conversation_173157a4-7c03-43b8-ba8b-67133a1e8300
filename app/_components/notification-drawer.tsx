'use client';
import dynamic from 'next/dynamic';
import { Drawer, CloseButton, Portal, Tabs } from '@chakra-ui/react';
import useNotificationStore from '@store/notification-store';

const NotificationContainer = dynamic(
  () => import('./notification/notification-container'),
  {
    ssr: false,
  }
);

/**
 * 通知抽屉组件
 *
 * 使用zustand状态管理控制抽屉的打开/关闭状态
 * 包含标题、内容区域和底部操作按钮（取消/保存）
 * 提供关闭按钮触发器
 *
 * @returns 返回一个可交互的通知抽屉组件
 */
export default function NotificationDrawer() {
  const open = useNotificationStore((state) => state.open);
  const handleNotificationState = useNotificationStore(
    (state) => state.handleNotificationState
  );
  return (
    <Drawer.Root
      open={open}
      onOpenChange={(e) => handleNotificationState(e.open)}
      size={'md'}
    >
      <Portal>
        <Drawer.Backdrop />
        <Drawer.Positioner>
          <Drawer.Content>
            <Drawer.Header>
              <Drawer.Title>消息通知</Drawer.Title>
            </Drawer.Header>
            <Drawer.Body>
              <Tabs.Root defaultValue="unread" variant={'outline'} fitted>
                <Tabs.List>
                  <Tabs.Trigger value="unread">未读</Tabs.Trigger>
                  <Tabs.Trigger value="read">已读</Tabs.Trigger>
                </Tabs.List>
                <Tabs.Content value="unread">
                  <NotificationContainer />
                </Tabs.Content>
                <Tabs.Content value="read">
                  <NotificationContainer />
                </Tabs.Content>
              </Tabs.Root>
            </Drawer.Body>
            <Drawer.CloseTrigger asChild>
              <CloseButton size="sm" />
            </Drawer.CloseTrigger>
          </Drawer.Content>
        </Drawer.Positioner>
      </Portal>
    </Drawer.Root>
  );
}
