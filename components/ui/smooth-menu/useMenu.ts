import { useState, useCallback } from 'react';

interface UseMenuProps {
  defaultSelectedKeys?: string[];
  defaultOpenKeys?: string[];
  onSelect?: (key: string) => void;
}

export const useMenu = ({
  defaultSelectedKeys = [],
  defaultOpenKeys = [],
  onSelect: onSelectProp,
}: UseMenuProps = {}) => {
  // 选中的菜单项
  const [selectedKeys, setSelectedKeys] =
    useState<string[]>(defaultSelectedKeys);

  // 展开的子菜单
  const [openKeys, setOpenKeys] = useState<string[]>(defaultOpenKeys);

  // 处理菜单项选择
  const handleSelect = useCallback(
    (key: string) => {
      setSelectedKeys([key]);
      onSelectProp?.(key);
    },
    [onSelectProp]
  );

  // 处理子菜单展开/收起
  const handleOpenChange = useCallback((key: string, open: boolean) => {
    setOpenKeys((prev) => {
      if (open) {
        return [...prev, key];
      } else {
        return prev.filter((k) => k !== key);
      }
    });
  }, []);

  // 检查菜单项是否被选中
  const isSelected = useCallback(
    (key: string) => {
      return selectedKeys.includes(key);
    },
    [selectedKeys]
  );

  // 检查子菜单是否展开
  const isOpen = useCallback(
    (key: string) => {
      return openKeys.includes(key);
    },
    [openKeys]
  );

  return {
    selectedKeys,
    openKeys,
    isSelected,
    isOpen,
    onSelect: handleSelect,
    onOpenChange: handleOpenChange,
  };
};
