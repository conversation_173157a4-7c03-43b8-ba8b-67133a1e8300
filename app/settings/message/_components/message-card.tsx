'use client';
import { Card, Stack, Avatar, Text, Blockquote } from '@chakra-ui/react';
import Link from 'next/link';

/**
 * 消息卡片组件
 *
 * 用于展示单条消息的卡片式UI，包含用户头像、名称、消息类型、
 * 消息内容、引用内容和时间戳等信息。
 *
 * @returns 返回一个可点击的消息卡片，点击后跳转到指定文章页面
 */
export default function MessageCard() {
  return (
    <Link href={'/articles/view/article_id'}>
      <Card.Root cursor={'pointer'} variant={'elevated'}>
        <Card.Body gap={2} p={4}>
          <Card.Header p={0}>
            <Stack direction="row" alignItems="center" gap={2}>
              <Avatar.Root size="lg">
                <Avatar.Image
                  src="https://avatars.githubusercontent.com/u/12345678?v=4"
                  alt="User Avatar"
                />
                <Avatar.Fallback>U</Avatar.Fallback>
              </Avatar.Root>
              <Stack>
                <Card.Title>steam摸鱼哥</Card.Title>
                <Card.Description>点赞了你的评论</Card.Description>
              </Stack>
            </Stack>
          </Card.Header>
          <Stack gap={4}>
            <Text textStyle="md">这是消息的主要内容部分。</Text>
            <Blockquote.Root>
              <Blockquote.Content>
                If anyone thinks he is something when he is nothing, he deceives
                himself. Each one should test his own actions. Then he can take
                pride in himself, without comparing himself to anyone else.
              </Blockquote.Content>
            </Blockquote.Root>
          </Stack>
        </Card.Body>
        <Card.Footer p={4} color="gray.500">
          2023-10-01 12:00
        </Card.Footer>
      </Card.Root>
    </Link>
  );
}
