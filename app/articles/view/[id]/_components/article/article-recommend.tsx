import { Card, Stack } from '@chakra-ui/react';
import ArticleCard from './card/article-card';
/**
 * 文章推荐组件
 *
 * 用于展示当前文章的推荐作品列表
 * 包含标题和作品列表内容区域
 *
 * @returns 返回包含推荐作品标题和列表的卡片组件
 */
export default function ArticleRecommend() {
  return (
    <Card.Root className="rounded-lg">
      <Card.Header borderBottom={'1px solid #eaeaea'} pb={2}>
        推荐作品
      </Card.Header>
      <Card.Body>
        <Stack gap={4}>
          <ArticleCard />
        </Stack>
      </Card.Body>
    </Card.Root>
  );
}
