import { Stack, Box, Flex, Heading } from '@chakra-ui/react';
import Link from 'next/link';
import {
  AiOutlineFolderOpen,
  AiOutlineCloudUpload,
  AiOutlineVideoCamera,
  AiOutlineFolder,
} from 'react-icons/ai';

export default function LeftSide() {
  return (
    <Stack gap={4} w={'100%'}>
      <Link href="/submission/upload">
        <Box className="flex h-12 w-full cursor-pointer items-center rounded-md !px-3 hover:bg-gray-100">
          <Flex gap={3} w={'100%'} alignItems={'center'}>
            <AiOutlineCloudUpload size={24} className="text-gray-500" />
            <Heading className="font-noto !text-base/6 font-medium !tracking-wider !text-gray-500">
              上传视频
            </Heading>
          </Flex>
        </Box>
      </Link>

      <Link href="/submission/manage">
        <Box className="flex h-12 w-full cursor-pointer items-center rounded-md !px-3 hover:bg-gray-100">
          <Flex gap={3} w={'100%'} alignItems={'center'}>
            <AiOutlineVideoCamera size={24} className="text-gray-500" />
            <Heading className="font-noto !text-base/6 font-medium !tracking-wider !text-gray-500">
              视频管理
            </Heading>
          </Flex>
        </Box>
      </Link>

      <Link href="/submission/folders">
        <Box className="flex h-12 w-full cursor-pointer items-center rounded-md !px-3 hover:bg-gray-100">
          <Flex gap={3} w={'100%'} alignItems={'center'}>
            <AiOutlineFolder size={24} className="text-gray-500" />
            <Heading className="font-noto !text-base/6 font-medium !tracking-wider !text-gray-500">
              文件夹管理
            </Heading>
          </Flex>
        </Box>
      </Link>
    </Stack>
  );
}
