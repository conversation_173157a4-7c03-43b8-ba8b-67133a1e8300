import React, { useState } from 'react';
import { Box, Button, Flex, Textarea } from '@chakra-ui/react';
import { useColorModeValue } from '@/components/ui/color-mode';
import { CommentInputProps } from './types';

export const CommentInput: React.FC<CommentInputProps> = ({
  onSubmit,
  placeholder = '写下你的评论...',
  isLoading = false,
}) => {
  const [content, setContent] = useState('');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const bgColor = useColorModeValue('white', 'gray.800');

  const handleSubmit = () => {
    if (content.trim()) {
      onSubmit(content.trim());
      setContent('');
    }
  };

  return (
    <Box
      borderWidth="1px"
      borderColor={borderColor}
      borderRadius="lg"
      p={4}
      bg={bgColor}
    >
      <Textarea
        value={content}
        onChange={(e) => setContent(e.target.value)}
        placeholder={placeholder}
        minH="100px"
        resize="vertical"
        mb={4}
        _focus={{
          borderColor: 'blue.500',
          boxShadow: 'none',
        }}
      />
      <Flex justifyContent="flex-end">
        <Button
          colorScheme="blue"
          loading={isLoading}
          disabled={!content.trim()}
          onClick={handleSubmit}
        >
          发表评论
        </Button>
      </Flex>
    </Box>
  );
};

export default CommentInput;
