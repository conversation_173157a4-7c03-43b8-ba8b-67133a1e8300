'use client';
import {
  Box,
  Flex,
  Card,
  Image,
  AspectRatio,
  Center,
  Text,
  Badge,
} from '@chakra-ui/react';
import NextImage from 'next/image';
import { AiFillEye, AiOutlineComment } from 'react-icons/ai';
import Paint from '@/public/images/paint.svg';

export default function VideoCard() {
  return (
    <Card.Root
      width="320px"
      borderWidth={'0px'}
      cursor={'pointer'}
      className="group"
    >
      <Card.Body padding="0px">
        <Flex gap="2" direction="column">
          <AspectRatio ratio={16 / 7}>
            <Center>
              <Box position="relative">
                <Image
                  asChild
                  className="h-full w-full rounded-md"
                  zIndex={999}
                >
                  <NextImage
                    src={Paint}
                    alt={'video image'}
                    width={120}
                    height={50}
                  />
                </Image>
              </Box>
              <Box
                position={'absolute'}
                width="100%"
                bottom="0px"
                left="0px"
                zIndex={1000}
              >
                <Box
                  width="100%"
                  bg="gray.400"
                  px={'24px'}
                  className="flex justify-between rounded-md p-2 opacity-50 group-hover:opacity-100"
                >
                  <Box className="flex gap-2">
                    <Badge variant="plain" px="0" color="white" size="md">
                      <AiFillEye />
                      5.2k
                    </Badge>
                    <Badge variant="plain" px="0" color="white" size="md">
                      <AiOutlineComment />
                      5.2k
                    </Badge>
                  </Box>
                  <Text color="white" className="!text-sm">
                    05:14
                  </Text>
                </Box>
              </Box>
            </Center>
          </AspectRatio>
          <Box mb={'5px'}>
            <Center>
              <Flex direction={'column'} width={'240px'}>
                <Text className="!text-md !font-semibold" lineClamp="2">
                  直到看见平凡，才是唯一的答案。
                </Text>
                <Flex gap="2">
                  <Text className="!text-sm !text-gray-500 dark:!text-white">
                    作者姓名
                  </Text>
                  <Text className="!text-sm !text-gray-500 dark:!text-white">
                    2022-03-10
                  </Text>
                </Flex>
              </Flex>
            </Center>
          </Box>
        </Flex>
      </Card.Body>
    </Card.Root>
  );
}
