'use client';

import { But<PERSON>, Box, Stack, Text } from '@chakra-ui/react';
import VideoEditDrawer from './VideoEditDrawer';
import { useVideoEditDrawer } from './useVideoEditDrawer';
import type { VideoData, VideoFormData, FolderOption } from './types';

// 示例视频数据
const sampleVideoData: VideoData = {
  id: '1',
  title: '我的第一个视频',
  description: '这是一个关于 React 开发的教程视频，包含了基础概念和实践案例。',
  coverImage: 'https://via.placeholder.com/320x180/4299e1/ffffff?text=Video+Cover',
  folderId: 'education',
  createdAt: '2024-01-01T00:00:00Z',
  duration: 1200, // 20分钟
  size: 104857600, // 100MB
  format: 'mp4',
};

// 示例文件夹数据
const sampleFolders: FolderOption[] = [
  { value: 'default', label: '默认文件夹', description: '系统默认文件夹' },
  { value: 'entertainment', label: '娱乐', description: '娱乐相关视频' },
  { value: 'education', label: '教育', description: '教育学习视频' },
  { value: 'technology', label: '科技', description: '科技类视频' },
  { value: 'lifestyle', label: '生活', description: '生活方式视频' },
  { value: 'gaming', label: '游戏', description: '游戏相关视频' },
  { value: 'music', label: '音乐', description: '音乐类视频' },
];

/**
 * 视频编辑抽屉使用示例组件
 * 
 * 展示如何使用 VideoEditDrawer 组件的完整示例
 */
export default function VideoEditDrawerExample() {
  const { isOpen, open, close } = useVideoEditDrawer();

  // 处理保存操作
  const handleSave = (data: VideoFormData) => {
    console.log('保存视频数据:', data);
    // 这里可以调用 API 保存数据
    // await saveVideoData(data);
    close();
  };

  // 处理发布操作
  const handlePublish = (data: VideoFormData) => {
    console.log('发布视频数据:', data);
    // 这里可以调用 API 发布视频
    // await publishVideo(data);
    close();
  };

  return (
    <Box p={6}>
      <Stack gap={4} maxW="md">
        <Text fontSize="xl" fontWeight="bold">
          视频编辑抽屉示例
        </Text>
        
        <Text color="gray.600">
          点击下面的按钮打开视频编辑抽屉，体验完整的编辑功能。
        </Text>

        <Button 
          colorScheme="blue" 
          onClick={open}
          size="lg"
        >
          编辑视频
        </Button>

        <Box p={4} bg="gray.50" borderRadius="md">
          <Text fontSize="sm" fontWeight="medium" mb={2}>
            当前视频信息：
          </Text>
          <Stack gap={1} fontSize="sm" color="gray.600">
            <Text>标题: {sampleVideoData.title}</Text>
            <Text>描述: {sampleVideoData.description}</Text>
            <Text>文件夹: {sampleFolders.find(f => f.value === sampleVideoData.folderId)?.label}</Text>
            <Text>时长: {Math.floor(sampleVideoData.duration! / 60)}分钟</Text>
          </Stack>
        </Box>
      </Stack>

      <VideoEditDrawer
        isOpen={isOpen}
        onClose={close}
        videoData={sampleVideoData}
        onSave={handleSave}
        onPublish={handlePublish}
        folders={sampleFolders}
      />
    </Box>
  );
}
