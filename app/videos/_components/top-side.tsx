'use client';
import { useState } from 'react';
import { Box, Button, IconButton, Separator } from '@chakra-ui/react';
import { AiOutlineUpload } from 'react-icons/ai';

export default function TopSide() {
  const [count, setCount] = useState(20);
  return (
    <Box height="94px" className="grid grid-cols-5 gap-3 rounded-md">
      <Box height="100%" className="col-span-4 flex shrink grow gap-2">
        <div className="flex w-full flex-wrap items-center gap-3">
          {Array.from({ length: count }).map((_, index) => (
            <Button
              variant="subtle"
              colorPalette={'gray'}
              w={'65px'}
              h={'30px'}
              key={index}
              overflow={'hidden'}
              textOverflow={'ellipsis'}
              whiteSpace={'nowrap'}
            >
              绘画-{index}
            </Button>
          ))}
        </div>
        <Separator orientation="vertical" />
      </Box>

      <Box className="col-span-1 flex items-center justify-center">
        <IconButton colorPalette={'pink'} w="85px" h={'35px'}>
          <AiOutlineUpload />
          投稿
        </IconButton>
      </Box>
    </Box>
  );
}
