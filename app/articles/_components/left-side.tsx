'use client';
import { useState, useEffect } from 'react';
import { Stack, IconButton, Box, Flex } from '@chakra-ui/react';
import { AiFillProduct, AiFillRead } from 'react-icons/ai';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';

// 创建 motion 版本的组件
const MotionFlex = motion(Flex);
const MotionIconButton = motion(IconButton);
const MotionBox = motion(Box);

export default function LeftSide() {
  const [items, setItems] = useState(16);

  return (
    <MotionFlex
      direction="column"
      h="100%"
      position="relative"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Stack
        gap={1}
        p={2}
        className="scroll-hover-hide overflow-y-auto scroll-smooth"
      >
        <MotionIconButton
          variant="ghost"
          className="active"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
          whileHover={{ scale: 1.05 }}
        >
          <AiFillProduct />
          全部文章
        </MotionIconButton>

        {Array.from({ length: items }).map((_, index) => (
          <MotionIconButton
            variant="ghost"
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.3,
              delay: 0.05 * Math.min(index, 10), // 限制最大延迟
            }}
            whileHover={{ scale: 1.05 }}
          >
            <AiFillRead />
            文章类别{index}
          </MotionIconButton>
        ))}
      </Stack>
    </MotionFlex>
  );
}
