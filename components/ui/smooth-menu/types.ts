import { ReactNode, CSSProperties } from 'react';

export interface MenuItem {
  key: string; // 唯一标识
  label: ReactNode; // 菜单项文本或节点
  icon?: ReactNode; // 图标
  disabled?: boolean; // 是否禁用
  command?: string; // 快捷键
  onClick?: () => void; // 点击回调
  children?: MenuItem[]; // 子菜单项
}

export interface SmoothMenuProps {
  items: MenuItem[]; // 菜单配置
  mode?: 'vertical' | 'horizontal'; // 菜单模式
  defaultSelectedKeys?: string[]; // 默认选中的项
  defaultOpenKeys?: string[]; // 默认展开的子菜单
  onSelect?: (key: string) => void; // 选择回调
  className?: string; // 自定义类名
  style?: CSSProperties; // 自定义样式
}

export interface MenuItemProps {
  item: MenuItem;
  isSelected: boolean;
  onSelect: (key: string) => void;
}

export interface SubMenuProps {
  item: MenuItem;
  isOpen: boolean;
  selectedKeys: string[];
  onSelect: (key: string) => void;
  onOpenChange: (key: string, open: boolean) => void;
}

export interface MenuContextType {
  selectedKeys: string[];
  openKeys: string[];
  onSelect: (key: string) => void;
  onOpenChange: (key: string, open: boolean) => void;
}
