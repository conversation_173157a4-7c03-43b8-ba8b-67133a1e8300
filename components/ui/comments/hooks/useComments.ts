import { useState, useCallback, useEffect } from 'react';
import { Comment } from '../types';

interface UseCommentsProps {
  initialComments?: Comment[];
  pageSize?: number;
  fetchComments?: (page: number, pageSize: number) => Promise<Comment[]>;
}

export const useComments = ({
  initialComments = [],
  pageSize = 5,
  fetchComments,
}: UseCommentsProps = {}) => {
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  // 加载评论
  const loadComments = useCallback(async () => {
    if (!fetchComments) return;

    setIsLoading(true);
    try {
      const newComments = await fetchComments(page, pageSize);
      if (newComments.length < pageSize) {
        setHasMore(false);
      }

      if (page === 1) {
        setComments(newComments);
      } else {
        setComments((prev) => [...prev, ...newComments]);
      }
    } catch (error) {
      console.error('Failed to load comments:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchComments, page, pageSize]);

  // 初始加载
  useEffect(() => {
    if (fetchComments && page === 1) {
      loadComments();
    }
  }, [fetchComments, loadComments, page]);

  // 加载更多评论
  const loadMore = useCallback(() => {
    setPage((prev) => prev + 1);
  }, []);

  // 添加评论
  const addComment = useCallback(
    (content: string, author: Comment['author']) => {
      const newComment: Comment = {
        id: `comment-${Date.now()}`,
        content,
        author,
        createdAt: new Date().toISOString(),
        likes: 0,
        replies: [],
      };

      setComments((prev) => [newComment, ...prev]);
    },
    []
  );

  // 添加回复
  const addReply = useCallback(
    (commentId: string, content: string, author: Comment['author']) => {
      const newReply: Comment = {
        id: `reply-${Date.now()}`,
        content,
        author,
        createdAt: new Date().toISOString(),
        likes: 0,
        parentId: commentId,
      };

      setComments((prev) => {
        return prev.map((comment) => {
          if (comment.id === commentId) {
            return {
              ...comment,
              replies: [...(comment.replies || []), newReply],
            };
          }
          return comment;
        });
      });
    },
    []
  );

  // 点赞评论
  const likeComment = useCallback((commentId: string) => {
    setComments((prev) => {
      return prev.map((comment) => {
        // 检查是否是主评论
        if (comment.id === commentId) {
          return { ...comment, likes: comment.likes + 1 };
        }

        // 检查是否是回复
        if (comment.replies) {
          const updatedReplies = comment.replies.map((reply) => {
            if (reply.id === commentId) {
              return { ...reply, likes: reply.likes + 1 };
            }
            return reply;
          });

          return { ...comment, replies: updatedReplies };
        }

        return comment;
      });
    });
  }, []);

  // 编辑评论
  const editComment = useCallback((commentId: string, content: string) => {
    setComments((prev) => {
      return prev.map((comment) => {
        // 检查是否是主评论
        if (comment.id === commentId) {
          return { ...comment, content };
        }

        // 检查是否是回复
        if (comment.replies) {
          const updatedReplies = comment.replies.map((reply) => {
            if (reply.id === commentId) {
              return { ...reply, content };
            }
            return reply;
          });

          return { ...comment, replies: updatedReplies };
        }

        return comment;
      });
    });
  }, []);

  // 删除评论
  const deleteComment = useCallback((commentId: string) => {
    // 删除主评论
    setComments((prev) => prev.filter((comment) => comment.id !== commentId));

    // 删除回复
    setComments((prev) => {
      return prev.map((comment) => {
        if (comment.replies) {
          return {
            ...comment,
            replies: comment.replies.filter((reply) => reply.id !== commentId),
          };
        }
        return comment;
      });
    });
  }, []);

  return {
    comments,
    hasMore,
    isLoading,
    loadMore,
    addComment,
    addReply,
    likeComment,
    editComment,
    deleteComment,
  };
};
