'use client';
import { Tabs, Box } from '@chakra-ui/react';
import { AiOutlineLineChart, AiFillFire } from 'react-icons/ai';
import ArticleCard from './card/article-card';
import { motion, AnimatePresence } from 'framer-motion';

// 创建 motion 版本的组件
const MotionBox = motion(Box);

export default function ArticleTabs() {
  // 图标动画变体
  const iconVariants = {
    hover: {
      scale: 1.2,
      rotate: [0, -10, 10, 0],
      transition: {
        duration: 0.3,
      },
    },
    tap: {
      scale: 0.9,
    },
  };

  return (
    <Tabs.Root defaultValue="hot">
      <Tabs.List className="relative">
        <motion.div
          className="absolute bottom-0 h-0.5"
          animate={{
            width: '50%',
          }}
          transition={{
            duration: 0.3,
            ease: 'easeInOut',
          }}
        />
        <Tabs.Trigger value="hot" className="relative w-1/2">
          <motion.div
            className="flex items-center justify-center gap-2"
            whileHover="hover"
            whileTap="tap"
          >
            <motion.div variants={iconVariants}>
              <AiFillFire className="text-red-500" />
            </motion.div>
            最热
          </motion.div>
        </Tabs.Trigger>
        <Tabs.Trigger value="new" className="relative w-1/2">
          <motion.div
            className="flex items-center justify-center gap-2"
            whileHover="hover"
            whileTap="tap"
          >
            <motion.div variants={iconVariants}>
              <AiOutlineLineChart className="text-blue-500" />
            </motion.div>
            最新
          </motion.div>
        </Tabs.Trigger>
      </Tabs.List>

      <AnimatePresence key="wait" mode="wait">
        <Tabs.Content value="hot" asChild>
          <MotionBox
            px="2"
            className="flex flex-col gap-3"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              <ArticleCard />
            </motion.div>
          </MotionBox>
        </Tabs.Content>
      </AnimatePresence>

      <AnimatePresence key="new" mode="wait">
        <Tabs.Content value="new" asChild>
          <MotionBox
            px="2"
            className="flex flex-col gap-3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              <ArticleCard />
            </motion.div>
          </MotionBox>
        </Tabs.Content>
      </AnimatePresence>
    </Tabs.Root>
  );
}
