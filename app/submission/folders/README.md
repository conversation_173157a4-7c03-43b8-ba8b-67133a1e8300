# 文件夹管理系统

## 功能概述

这是一个完整的文件夹管理系统，支持文件夹的创建、编辑、删除以及视频内容管理。

## 主要功能

### 1. 文件夹管理
- ✅ 创建新文件夹
- ✅ 编辑文件夹信息（名称、描述、颜色）
- ✅ 删除文件夹（默认文件夹受保护）
- ✅ 文件夹统计信息显示

### 2. 文件夹详情查看
- ✅ 点击自定义文件夹查看详情
- ✅ 右侧 Drawer 显示文件夹信息
- ✅ 文件夹内视频列表展示
- ✅ 视频缩略图和基本信息

### 3. 视频管理
- ✅ 视频列表展示（缩略图、标题、描述、时长、大小、状态）
- ✅ 视频编辑功能（通过 Modal 显示）
- ✅ 视频移动功能（移动到其他文件夹）
- ✅ 视频删除功能（带确认对话框）

## 技术特点

### 组件拆分
- 主页面：`page.tsx`
- 文件夹编辑：`FolderEditDrawer.tsx`
- 文件夹详情：`FolderDetailDrawer.tsx`
- 视频列表：`VideoList.tsx`
- 视频编辑：`VideoEditModal.tsx`
- 视频移动：`VideoMoveModal.tsx`
- 视频删除：`VideoDeleteDialog.tsx`
- 删除确认：`DeleteConfirmDialog.tsx`
- 类型定义：`types.ts`

### 性能优化
- 组件按功能拆分，避免单文件过大
- 使用 React.memo 和 useCallback 优化渲染
- 懒加载模态框和对话框
- 合理的状态管理

### 用户体验
- 响应式设计，支持不同屏幕尺寸
- 流畅的动画和过渡效果
- 直观的操作反馈
- 安全的删除确认机制

## 使用说明

1. **查看文件夹**：访问 `/submission/folders` 页面
2. **创建文件夹**：点击"新建文件夹"按钮
3. **编辑文件夹**：点击文件夹卡片上的菜单按钮选择"编辑"
4. **查看详情**：点击自定义文件夹卡片（默认文件夹不支持）
5. **管理视频**：在文件夹详情中查看和管理视频

## 注意事项

- 默认文件夹（ID为1）不能删除，也不支持详情查看
- 自定义文件夹支持完整的 CRUD 操作
- 视频移动时会从当前文件夹中移除
- 所有删除操作都有确认机制，防止误操作

## 更新内容

### 最新更新
- ❌ 移除了文章相关功能和统计
- ✅ 添加了文件夹详情 Drawer
- ✅ 实现了完整的视频管理功能
- ✅ 优化了组件结构和性能
- ✅ 改进了用户交互体验
