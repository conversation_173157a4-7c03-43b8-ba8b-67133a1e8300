import { Box, IconButton, Stack } from '@chakra-ui/react';
import { Tooltip } from '@/components/ui/tooltip';
import { AiFillLike, AiOutlineLike } from 'react-icons/ai';

export default function VideoAction() {
  return (
    <Box py={4} borderBottom={'2px solid #eee'}>
      <Stack direction="row" gap="4">
        <Tooltip content="点赞">
          <IconButton
            aria-label="点赞"
            variant={'ghost'}
            bg={'primary'}
            color={'white'}
          >
            {/**
             * 根据是否点赞修改icon
             */}
            <AiFillLike />
            2.6万
          </IconButton>
        </Tooltip>
      </Stack>
    </Box>
  );
}
