'use client';

import React from 'react';
import {
  Dialog,
  Portal,
  Stack,
  Field,
  Input,
  Textarea,
  Button,
  Box,
  Text,
  Flex,
  Image,
  AspectRatio,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { object, string } from 'yup';
import { AiOutlinePlayCircle } from 'react-icons/ai';
import { Video, VideoFormData } from './types';

// 组件 Props 类型
interface VideoEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  video: Video | null;
  onSave: (videoId: string, data: VideoFormData) => void;
}

// 表单验证 schema
const schema = object({
  title: string()
    .required('请输入视频标题')
    .min(1, '视频标题不能为空')
    .max(50, '视频标题不能超过50个字符'),
  description: string()
    .required('请输入视频描述')
    .min(1, '视频描述不能为空')
    .max(200, '视频描述不能超过200个字符'),
});

// 格式化时长
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  const mb = bytes / (1024 * 1024);
  return `${mb.toFixed(1)} MB`;
};

export default function VideoEditModal({
  isOpen,
  onClose,
  video,
  onSave,
}: VideoEditModalProps) {
  // 表单配置
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<VideoFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      title: video?.title || '',
      description: video?.description || '',
      folderId: video?.folderId || '',
    },
  });

  // 处理保存
  const handleSave = (data: VideoFormData) => {
    if (video) {
      onSave(video.id, data);
    }
  };

  // 处理关闭
  const handleClose = () => {
    reset();
    onClose();
  };

  // 当 video 改变时重置表单
  React.useEffect(() => {
    if (isOpen && video) {
      reset({
        title: video.title,
        description: video.description,
        folderId: video.folderId,
      });
    }
  }, [video, isOpen, reset]);

  if (!video) return null;

  return (
    <Dialog.Root open={isOpen} onOpenChange={(e) => !e.open && handleClose()}>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content maxW="2xl">
            <Dialog.Header>
              <Dialog.Title>编辑视频</Dialog.Title>
              <Dialog.CloseTrigger />
            </Dialog.Header>

            <Dialog.Body>
              <form id="video-edit-form" onSubmit={handleSubmit(handleSave)}>
                <Stack gap={6}>
                  {/* 视频预览 */}
                  <Box>
                    <Text fontSize="sm" fontWeight="medium" mb={3}>
                      视频预览
                    </Text>
                    <Flex gap={4}>
                      <Box width="200px" flexShrink={0} position="relative">
                        <AspectRatio ratio={16 / 9}>
                          <Image
                            src={video.coverImage}
                            alt={video.title}
                            borderRadius="md"
                            objectFit="cover"
                          />
                        </AspectRatio>
                        <Flex
                          position="absolute"
                          bottom={2}
                          right={2}
                          bg="blackAlpha.700"
                          color="white"
                          px={2}
                          py={1}
                          borderRadius="sm"
                          fontSize="xs"
                          align="center"
                          gap={1}
                        >
                          <AiOutlinePlayCircle size={12} />
                          <Text>{formatDuration(video.duration)}</Text>
                        </Flex>
                      </Box>

                      <Box flex={1}>
                        <Stack gap={2} fontSize="sm">
                          <Box>
                            <Text fontWeight="medium" color="gray.700">
                              文件大小
                            </Text>
                            <Text color="gray.600">
                              {formatFileSize(video.size)}
                            </Text>
                          </Box>
                          <Box>
                            <Text fontWeight="medium" color="gray.700">
                              创建时间
                            </Text>
                            <Text color="gray.600">
                              {new Date(video.createdAt).toLocaleString(
                                'zh-CN'
                              )}
                            </Text>
                          </Box>
                          {video.updatedAt && (
                            <Box>
                              <Text fontWeight="medium" color="gray.700">
                                更新时间
                              </Text>
                              <Text color="gray.600">
                                {new Date(video.updatedAt).toLocaleString(
                                  'zh-CN'
                                )}
                              </Text>
                            </Box>
                          )}
                        </Stack>
                      </Box>
                    </Flex>
                  </Box>

                  {/* 视频标题 */}
                  <Field.Root invalid={!!errors.title} required>
                    <Field.Label>
                      <Field.RequiredIndicator />
                      视频标题
                    </Field.Label>
                    <Input
                      placeholder="请输入视频标题"
                      {...register('title')}
                    />
                    <Field.ErrorText>{errors.title?.message}</Field.ErrorText>
                    <Field.HelperText>
                      简洁明了的标题有助于观众理解视频内容
                    </Field.HelperText>
                  </Field.Root>

                  {/* 视频描述 */}
                  <Field.Root invalid={!!errors.description} required>
                    <Field.Label>
                      <Field.RequiredIndicator />
                      视频描述
                    </Field.Label>
                    <Textarea
                      placeholder="请输入视频描述"
                      rows={4}
                      {...register('description')}
                    />
                    <Field.ErrorText>
                      {errors.description?.message}
                    </Field.ErrorText>
                    <Field.HelperText>
                      详细描述视频内容，帮助观众了解视频主题
                    </Field.HelperText>
                  </Field.Root>
                </Stack>
              </form>
            </Dialog.Body>

            <Dialog.Footer>
              <Stack direction="row" gap={3} width="100%">
                <Button variant="outline" onClick={handleClose} flex={1}>
                  取消
                </Button>
                <Button
                  type="submit"
                  form="video-edit-form"
                  colorScheme="blue"
                  isLoading={isSubmitting}
                  flex={1}
                >
                  保存更改
                </Button>
              </Stack>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
