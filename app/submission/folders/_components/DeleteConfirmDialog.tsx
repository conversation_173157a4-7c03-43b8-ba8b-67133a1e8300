'use client';

import {
  Dialog,
  Portal,
  Stack,
  Button,
  Text,
  Box,
  Flex,
  Alert,
} from '@chakra-ui/react';
import { AiOutlineFolderOpen, AiOutlineWarning } from 'react-icons/ai';
import { Folder } from './types';

// 组件 Props 类型
interface DeleteConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  folder: Folder | null;
  onConfirm: () => void;
}

export default function DeleteConfirmDialog({
  isOpen,
  onClose,
  folder,
  onConfirm,
}: DeleteConfirmDialogProps) {
  if (!folder) return null;

  const hasContent = folder.videoCount > 0;
  const isDefaultFolder = folder.id === '1';

  return (
    <Dialog.Root open={isOpen} onOpenChange={(e) => !e.open && onClose()}>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content maxW="md">
            <Dialog.Header>
              <Dialog.Title>删除文件夹</Dialog.Title>
              <Dialog.CloseTrigger />
            </Dialog.Header>

            <Dialog.Body>
              <Stack gap={4}>
                {/* 文件夹信息 */}
                <Box
                  p={4}
                  borderRadius="md"
                  border="1px solid"
                  borderColor="gray.200"
                  bg="gray.50"
                >
                  <Flex align="center" gap={3}>
                    <Box
                      p={2}
                      borderRadius="md"
                      bg={`${folder.color}.100`}
                      color={`${folder.color}.600`}
                    >
                      <AiOutlineFolderOpen size={20} />
                    </Box>
                    <Box>
                      <Text fontWeight="medium" fontSize="md">
                        {folder.name}
                      </Text>
                      <Text fontSize="sm" color="gray.600">
                        {folder.description}
                      </Text>
                    </Box>
                  </Flex>

                  {/* 内容统计 */}
                  <Box
                    mt={3}
                    pt={3}
                    borderTop="1px solid"
                    borderColor="gray.200"
                  >
                    <Flex gap={4} fontSize="sm" color="gray.600">
                      <Text>{folder.videoCount} 个视频</Text>
                    </Flex>
                  </Box>
                </Box>

                {/* 警告信息 */}
                {isDefaultFolder ? (
                  <Alert.Root status="error">
                    <Alert.Indicator>
                      <AiOutlineWarning />
                    </Alert.Indicator>
                    <Alert.Title>无法删除默认文件夹</Alert.Title>
                    <Alert.Description>
                      默认文件夹是系统必需的，无法删除。
                    </Alert.Description>
                  </Alert.Root>
                ) : hasContent ? (
                  <Alert.Root status="warning">
                    <Alert.Indicator>
                      <AiOutlineWarning />
                    </Alert.Indicator>
                    <Alert.Title>注意：文件夹包含内容</Alert.Title>
                    <Alert.Description>
                      删除文件夹后，其中的 {folder.videoCount}{' '}
                      个视频将被移动到默认文件夹中。
                    </Alert.Description>
                  </Alert.Root>
                ) : (
                  <Alert.Root status="info">
                    <Alert.Indicator>
                      <AiOutlineWarning />
                    </Alert.Indicator>
                    <Alert.Title>确认删除</Alert.Title>
                    <Alert.Description>
                      此操作无法撤销，请确认是否要删除此文件夹。
                    </Alert.Description>
                  </Alert.Root>
                )}

                {/* 删除确认文本 */}
                {!isDefaultFolder && (
                  <Box>
                    <Text fontSize="sm" color="gray.700">
                      您确定要删除文件夹{' '}
                      <Text as="span" fontWeight="bold">
                        "{folder.name}"
                      </Text>{' '}
                      吗？
                    </Text>
                    {hasContent && (
                      <Text fontSize="sm" color="gray.600" mt={2}>
                        文件夹中的内容将自动移动到默认文件夹，不会丢失。
                      </Text>
                    )}
                  </Box>
                )}
              </Stack>
            </Dialog.Body>

            <Dialog.Footer>
              <Stack direction="row" gap={3} width="100%">
                <Button variant="outline" onClick={onClose} flex={1}>
                  取消
                </Button>
                {!isDefaultFolder && (
                  <Button colorScheme="red" onClick={onConfirm} flex={1}>
                    确认删除
                  </Button>
                )}
              </Stack>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
