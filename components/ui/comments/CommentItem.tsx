import React, { useState } from 'react';
import {
  Box,
  Flex,
  Text,
  Avatar,
  Button,
  IconButton,
  Menu,
  Textarea,
  Portal,
  Collapsible,
  Separator,
  Stack,
} from '@chakra-ui/react';
import { FaH<PERSON>t, FaReply, FaEllipsisV, FaEdit, FaTrash } from 'react-icons/fa';
import { useColorModeValue } from '@/components/ui/color-mode';
import { Comment, CommentItemProps } from './types';
import CommentInput from './CommentInput';

export const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  onReply,
  onLike,
  onEdit,
  onDelete,
  currentUser,
}) => {
  const [isReplying, setIsReplying] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(comment.content);
  const [showReplies, setShowReplies] = useState(true);

  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const bgColor = useColorModeValue('white', 'gray.800');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');

  const isAuthor = currentUser?.id === comment.author.id;
  const hasReplies = comment.replies && comment.replies.length > 0;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const handleReplySubmit = (content: string) => {
    onReply(comment.id, content);
    setIsReplying(false);
  };

  const handleEditSubmit = () => {
    if (editContent.trim() !== comment.content) {
      onEdit(comment.id, editContent);
    }
    setIsEditing(false);
  };

  const handleDelete = () => {
    onDelete(comment.id);
  };

  return (
    <Box>
      <Box p={4} bg={bgColor}>
        <Flex mb={4}>
          <Avatar.Root size="sm" mr={3}>
            <Avatar.Image
              src={comment.author.avatar}
              alt={comment.author.name}
            />
            <Avatar.Fallback>{comment.author.name[0]}</Avatar.Fallback>
          </Avatar.Root>
          <Box flex="1">
            <Flex justifyContent="space-between" alignItems="center">
              <Text fontWeight="bold">{comment.author.name}</Text>
              {isAuthor && (
                <Menu.Root>
                  <Menu.Trigger asChild>
                    <IconButton variant="ghost" size="sm" aria-label="更多选项">
                      <FaEllipsisV />
                    </IconButton>
                  </Menu.Trigger>
                  <Portal>
                    <Menu.Positioner>
                      <Menu.Content>
                        <Menu.Item
                          value="edit"
                          onClick={() => setIsEditing(true)}
                        >
                          <FaEdit />
                          编辑
                        </Menu.Item>
                        <Menu.Item value="delete" onClick={handleDelete}>
                          <FaTrash />
                          删除
                        </Menu.Item>
                      </Menu.Content>
                    </Menu.Positioner>
                  </Portal>
                </Menu.Root>
              )}
            </Flex>
            <Text fontSize="sm" color="gray.500" mb={2}>
              {formatDate(comment.createdAt)}
            </Text>
          </Box>
        </Flex>

        {isEditing ? (
          <Box mb={3}>
            <Textarea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              mb={2}
            />
            <Flex justifyContent="flex-end">
              <Button size="sm" mr={2} onClick={() => setIsEditing(false)}>
                取消
              </Button>
              <Button
                size="sm"
                colorScheme="blue"
                onClick={handleEditSubmit}
                disabled={!editContent.trim()}
              >
                保存
              </Button>
            </Flex>
          </Box>
        ) : (
          <Text mb={4}>{comment.content}</Text>
        )}

        <Flex>
          <IconButton
            size="sm"
            variant="ghost"
            onClick={() => onLike(comment.id)}
            mr={2}
          >
            <FaHeart />
            {comment.likes > 0 ? `${comment.likes}` : '点赞'}
          </IconButton>
          <IconButton
            size="sm"
            variant="ghost"
            onClick={() => setIsReplying(!isReplying)}
          >
            <FaReply />
            回复
          </IconButton>
        </Flex>
      </Box>

      {isReplying && (
        <Box mt={4} ml={{ base: 0, md: 10 }}>
          <CommentInput
            onSubmit={handleReplySubmit}
            placeholder="写下你的回复..."
          />
        </Box>
      )}

      {hasReplies && (
        <Box mt={4}>
          <Flex
            alignItems="center"
            mb={2}
            cursor="pointer"
            onClick={() => setShowReplies(!showReplies)}
          >
            <Separator flex="1" borderColor={borderColor} />
            <Text px={2} fontSize="sm" color="gray.500">
              {showReplies
                ? '隐藏回复'
                : `显示 ${comment.replies?.length} 条回复`}
            </Text>
            <Separator flex="1" borderColor={borderColor} />
          </Flex>
          {showReplies && (
            <Box ml={{ base: 4, md: 10 }}>
              {comment.replies?.map((reply) => (
                <ReplyItem
                  key={reply.id}
                  reply={reply}
                  onLike={onLike}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  currentUser={currentUser}
                />
              ))}
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};

interface ReplyItemProps {
  reply: Comment;
  onLike: (commentId: string) => void;
  onEdit: (commentId: string, content: string) => void;
  onDelete: (commentId: string) => void;
  currentUser?: Comment['author'];
}

const ReplyItem: React.FC<ReplyItemProps> = ({
  reply,
  onLike,
  onEdit,
  onDelete,
  currentUser,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(reply.content);

  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const bgColor = useColorModeValue('white', 'gray.800');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');

  const isAuthor = currentUser?.id === reply.author.id;
  const [isReplying, setIsReplying] = useState(false);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const handleEditSubmit = () => {
    if (editContent.trim() !== reply.content) {
      onEdit(reply.id, editContent);
    }
    setIsEditing(false);
  };

  const handleDelete = () => {
    onDelete(reply.id);
  };

  return (
    <Box bg={bgColor} mb={3}>
      <Flex>
        <Avatar.Root size="xs" mr={2}>
          <Avatar.Image src={reply.author.avatar} alt={reply.author.name} />
          <Avatar.Fallback>{reply.author.name[0]}</Avatar.Fallback>
        </Avatar.Root>
        <Stack>
          <Box flexShrink={0} minWidth="fit-content">
            <Text
              display="inline"
              whiteSpace={'nowrap'}
              fontWeight="bold"
              fontSize="sm"
              flexShrink={0} // 防止用户名被压缩
            >
              {reply.author.name}:
            </Text>

            {isEditing ? (
              <Box>
                <Textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  size="sm"
                  mb={2}
                  width="100%"
                />
                <Flex justifyContent="flex-end">
                  <Button size="xs" mr={2} onClick={() => setIsEditing(false)}>
                    取消
                  </Button>
                  <Button
                    size="xs"
                    colorScheme="blue"
                    onClick={handleEditSubmit}
                    disabled={!editContent.trim()}
                  >
                    保存
                  </Button>
                </Flex>
              </Box>
            ) : (
              <Text
                fontSize="sm"
                as="span"
                display="inline"
                color="fg.muted"
                ml={2}
              >
                {reply.content}
              </Text>
            )}
          </Box>

          <Flex justify={'space-between'} direction="row">
            <Stack direction="row" gap={4} className="flex items-center">
              <Text fontSize="xs" color="gray.500" mb={1}>
                {formatDate(reply.createdAt)}
              </Text>
              <IconButton
                color="fg.muted"
                size="xs"
                variant="ghost"
                onClick={() => onLike(reply.id)}
              >
                <FaHeart />
                {reply.likes > 0 ? `${reply.likes}` : '点赞'}
              </IconButton>
              <IconButton
                size="sm"
                variant="ghost"
                color="fg.muted"
                onClick={() => setIsReplying(!isReplying)}
              >
                <FaReply />
                回复
              </IconButton>
            </Stack>

            {isAuthor && (
              <Menu.Root>
                <Menu.Trigger asChild>
                  <IconButton variant="ghost" size="sm" aria-label="更多选项">
                    <FaEllipsisV />
                  </IconButton>
                </Menu.Trigger>
                <Portal>
                  <Menu.Positioner>
                    <Menu.Content>
                      <Menu.Item
                        value="edit"
                        onClick={() => setIsEditing(true)}
                      >
                        <FaEdit />
                        编辑
                      </Menu.Item>
                      <Menu.Item value="delete" onClick={handleDelete}>
                        <FaTrash />
                        删除
                      </Menu.Item>
                    </Menu.Content>
                  </Menu.Positioner>
                </Portal>
              </Menu.Root>
            )}
          </Flex>
          {isReplying && (
            <Box mt={4} ml={{ base: 0, md: 10 }}>
              <CommentInput
                placeholder="写下你的回复..."
                onSubmit={console.log}
              />
            </Box>
          )}
        </Stack>
      </Flex>
    </Box>
  );
};

export default CommentItem;
