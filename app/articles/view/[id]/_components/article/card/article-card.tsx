'use client';
import { Box, Badge, Stack, Text } from '@chakra-ui/react';
import { useState } from 'react';

/**
 * 文章卡片组件，用于展示推荐文章列表
 *
 * @returns 返回一组文章卡片元素，包含标题、作者、点赞数和阅读量等信息
 * @remarks
 * - 使用 `useState` 控制渲染的文章卡片数量
 * - 每张卡片包含标题、原创标识、用户名、点赞数和阅读量
 * - 标题在悬停时显示主色
 * - 使用 `Badge` 组件标记原创、点赞和阅读状态
 */
export default function ArticleCard() {
  const [count, setCount] = useState(10);
  return Array.from({ length: count }).map((_, index) => (
    <Box className="group flex cursor-pointer flex-col gap-2" key={index}>
      <Text
        className="group-hover:!text-primary !text-sm font-semibold"
        truncate
      >
        推荐作品标题
      </Text>
      <Stack direction={'row'} gap={3}>
        <Text className="!text-foreground !text-xs">
          <Badge colorScheme="green">原创</Badge>
          用户名
        </Text>
        <Text className="!text-foreground !text-xs">
          <Badge colorScheme="green">点赞</Badge>
          356万
        </Text>
        <Text className="!text-foreground !text-xs">
          <Badge colorScheme="green">阅读</Badge>
          350万
        </Text>
      </Stack>
    </Box>
  ));
}
