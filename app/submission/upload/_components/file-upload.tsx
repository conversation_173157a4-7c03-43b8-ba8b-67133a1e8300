import { Box, FileUpload, Icon, Center } from '@chakra-ui/react';
import { LuUpload } from 'react-icons/lu';

export default function FileUploadView() {
  return (
    <Center className="!mt-10">
      <FileUpload.Root
        maxW="xl"
        alignItems="stretch"
        maxFiles={5}
        accept={'video/*'}
      >
        <FileUpload.HiddenInput />
        <FileUpload.Dropzone>
          <Icon size="md" color="fg.muted">
            <LuUpload />
          </Icon>
          <FileUpload.DropzoneContent>
            <Box>将视频拖拽此处上传</Box>
            <Box color="fg.muted">
              上传的视频可以在草稿箱找到哦，通过审核就可以发布啦
            </Box>
          </FileUpload.DropzoneContent>
        </FileUpload.Dropzone>
        <FileUpload.List showSize clearable />
      </FileUpload.Root>
    </Center>
  );
}
