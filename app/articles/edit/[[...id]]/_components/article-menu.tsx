'use client';
import { memo } from 'react';
import {
  Stack,
  Field,
  Input,
  Box,
  RadioGroup,
  FileUpload,
  Icon,
  Textarea,
  Text,
} from '@chakra-ui/react';
import dynamic from 'next/dynamic';
import { LuUpload } from 'react-icons/lu';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { object, string, array } from 'yup';
import type { InitialValueState, Tags } from '@/app/articles/types';

// import { CreatableSelect } from 'chakra-react-select';
const CreatableSelect = dynamic(
  () => import('chakra-react-select').then((mod) => mod.CreatableSelect),
  { ssr: false }
);

// 类别
const items = [
  { value: '1', label: '文章' },
  { value: '2', label: '视频' },
  { value: '3', label: '音频' },
  { value: '4', label: '图片' },
  { value: '5', label: '链接' },
  { value: '6', label: '其他' },
];

// 标签
const options = [
  { value: '1', label: '编程' },
  { value: '2', label: '技术' },
  { value: '3', label: '教程' },
  { value: '4', label: '图片' },
  { value: '5', label: '链接' },
  { value: '6', label: '其他' },
];

interface IProps {
  initialValue: InitialValueState;
  onSubmit: (data: InitialValueState) => void;
}
/**
 * 文章菜单组件 - 提供文章编辑页面的表单输入区域
 *
 * 包含以下表单字段：
 * - 文章类别（单选）
 * - 文章标题（文本输入）
 * - 文章标签（多选/可创建，最多2个）
 * - 文章封面（文件上传）
 * - 文章描述（文本域，带字数统计）
 *
 * 所有必填字段都标有红色必填标记
 * 使用Fieldset组件进行整体布局和分组
 */
const ArticleMenu = ({ initialValue, onSubmit }: IProps) => {
  const maxLength = 100;
  const schema = object({
    categories: string().required('请选择文章类别'),
    title: string().required('请输入文章标题'),
    tags: array()
      .of(
        object({
          label: string().required(),
          value: string().required(),
        })
      )
      .min(1, '请选择文章标签')
      .max(2, '最多选择2个标签')
      .required('请选择文章标签'),
    content: string().required('请输入文章内容'),
  });
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<InitialValueState>({
    resolver: yupResolver(schema),
  });

  return (
    <form id="article-form" onSubmit={handleSubmit(onSubmit)}>
      <Stack gap={4}>
        <Field.Root
          orientation="horizontal"
          invalid={!!errors.categories}
          required
        >
          <Field.Label>
            <Field.RequiredIndicator />
            文章类别
          </Field.Label>
          <Stack w="100%">
            <Controller
              name="categories"
              control={control}
              render={({ field }) => (
                <>
                  <RadioGroup.Root
                    size="sm"
                    name={field.name}
                    value={field.value}
                    onValueChange={({ value }) => {
                      field.onChange(value);
                    }}
                  >
                    <Box display={'flex'} flexWrap={'wrap'} className="gap-2">
                      {items.map((item) => (
                        <RadioGroup.Item key={item.value} value={item.value}>
                          <RadioGroup.ItemHiddenInput />
                          <RadioGroup.ItemIndicator />
                          <RadioGroup.ItemText>
                            {item.label}
                          </RadioGroup.ItemText>
                        </RadioGroup.Item>
                      ))}
                    </Box>
                  </RadioGroup.Root>
                </>
              )}
            />
            <Field.ErrorText>{errors.categories?.message}</Field.ErrorText>
          </Stack>
        </Field.Root>
        <Field.Root orientation="horizontal" invalid={!!errors.title} required>
          <Field.Label>
            <Field.RequiredIndicator />
            文章标题
          </Field.Label>
          <Stack w="100%">
            <Input placeholder="请输入文章标题" {...register('title')} />
            <Field.ErrorText>{errors.title?.message}</Field.ErrorText>
          </Stack>
        </Field.Root>
        <Field.Root orientation="horizontal" invalid={!!errors.tags} required>
          <Field.Label>
            <Field.RequiredIndicator />
            文章标签
          </Field.Label>
          <Stack w="100%">
            <Controller
              control={control}
              name="tags"
              render={({ field }) => (
                <CreatableSelect
                  isMulti
                  tagVariant="outline"
                  options={options}
                  name={field.name}
                  value={field.value}
                  onChange={field.onChange}
                  getOptionLabel={(option: Tags) => option.label}
                  getOptionValue={(option: Tags) => option.value}
                  placeholder="选择或输入新标签(最多2个)"
                  isOptionDisabled={() => initialValue.tags?.length >= 2}
                  closeMenuOnSelect={false}
                  chakraStyles={{
                    dropdownIndicator: (provided) => ({
                      ...provided,
                      bg: 'transparent',
                      px: 2,
                      cursor: 'inherit',
                    }),
                    indicatorSeparator: (provided) => ({
                      ...provided,
                      display: 'none',
                    }),
                  }}
                />
              )}
            />
            <Field.ErrorText>{errors.tags?.message}</Field.ErrorText>
          </Stack>
        </Field.Root>
        <Field.Root orientation="horizontal">
          <Field.Label>文章封面</Field.Label>
          <FileUpload.Root alignItems="stretch" maxFiles={1}>
            <Stack w="100%">
              <FileUpload.HiddenInput />
              <FileUpload.Dropzone>
                <Icon size="sm" color="bg.muted">
                  <LuUpload />
                </Icon>
                <FileUpload.DropzoneContent>
                  <Box>将图片拖放上传封面</Box>
                  <Box color="fg.muted">.png, .jpg up to 5MB</Box>
                </FileUpload.DropzoneContent>
              </FileUpload.Dropzone>
              <Field.HelperText>建议尺寸: 192 * 128px</Field.HelperText>
            </Stack>
            <FileUpload.List />
          </FileUpload.Root>
        </Field.Root>
        <Field.Root
          orientation="horizontal"
          invalid={!!errors.content}
          required
        >
          <Field.Label>
            <Field.RequiredIndicator />
            文章描述
          </Field.Label>
          <Stack w="100%">
            <Textarea placeholder="请输入文章描述" {...register('content')} />
            <Field.HelperText>
              <Text
                fontSize="sm"
                color={
                  initialValue.content.length >= maxLength
                    ? 'red.500'
                    : 'gray.500'
                }
              >
                {initialValue.content.length}/{maxLength}
              </Text>
            </Field.HelperText>
            <Field.ErrorText>{errors.content?.message}</Field.ErrorText>
          </Stack>
        </Field.Root>
      </Stack>
    </form>
  );
};

export default memo(ArticleMenu);
