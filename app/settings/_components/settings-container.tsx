import { Container, Flex } from '@chakra-ui/react';
import SettingsBreadcrumb from '@/app/settings/_components/settings-breadcrumb';

interface IProps {
  children: React.ReactNode;
  title: string;
}

export default function SettingsContainer({ children, title }: IProps) {
  return (
    <Flex direction={'column'} gap={6} p={4}>
      <SettingsBreadcrumb title={title} />
      <Container
        maxW="100%"
        paddingInline={0}
        className="grid grid-cols-2 gap-4 rounded-md"
      >
        {children}
      </Container>
    </Flex>
  );
}
