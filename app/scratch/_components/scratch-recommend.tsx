'use client';
import { Card, Image, Box, Stack } from '@chakra-ui/react';
import RecommendCard from './card/recommend-card';

export default function ScratchRecommend() {
  return (
    <Card.Root className="rounded-md" border={'none'} cursor={'pointer'}>
      <Card.Header
        className="!text-md !font-semibold tracking-wide"
        textAlign={'center'}
        border={'2px solid #e2e8f0'}
      >
        推荐作品
      </Card.Header>
      <Card.Body p={0}>
        <Stack gap={4} className="py-4" my={4}>
          <RecommendCard />
          <RecommendCard />
        </Stack>
      </Card.Body>
    </Card.Root>
  );
}
