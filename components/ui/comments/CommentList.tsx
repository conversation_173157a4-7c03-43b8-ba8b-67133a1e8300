import React from 'react';
import { Box, Button, Center, Spinner, Text, VStack } from '@chakra-ui/react';
import { useColorModeValue } from '@/components/ui/color-mode';
import { CommentListProps } from './types';
import CommentItem from './CommentItem';

export const CommentList: React.FC<
  CommentListProps & {
    onReply: (commentId: string, content: string) => void;
    onLike: (commentId: string) => void;
    onEdit: (commentId: string, content: string) => void;
    onDelete: (commentId: string) => void;
    currentUser?: { id: string; name: string; avatar: string };
  }
> = ({
  comments,
  onLoadMore,
  hasMore,
  isLoading,
  onReply,
  onLike,
  onEdit,
  onDelete,
  currentUser,
}) => {
  const emptyBgColor = useColorModeValue('gray.50', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  if (comments.length === 0 && !isLoading) {
    return (
      <Center
        p={8}
        borderWidth="1px"
        borderColor={borderColor}
        borderRadius="lg"
        bg={emptyBgColor}
      >
        <Text color="gray.500">暂无评论，快来发表第一条评论吧！</Text>
      </Center>
    );
  }

  return (
    <Box>
      <VStack gap={6} align="stretch">
        {comments.map((comment) => (
          <CommentItem
            key={comment.id}
            comment={comment}
            onReply={onReply}
            onLike={onLike}
            onEdit={onEdit}
            onDelete={onDelete}
            currentUser={currentUser}
          />
        ))}
      </VStack>

      {hasMore && (
        <Center mt={6}>
          <Button
            onClick={onLoadMore}
            loading={isLoading}
            loadingText="加载中"
            variant="outline"
            colorScheme="blue"
          >
            加载更多评论
          </Button>
        </Center>
      )}

      {isLoading && !hasMore && (
        <Center mt={6}>
          <Spinner size="md" color="blue.500" />
        </Center>
      )}
    </Box>
  );
};

export default CommentList;
