import dynamic from 'next/dynamic';
import { Box, Flex } from '@chakra-ui/react';

const VideoAggregate = dynamic(() => import('./video-aggregate'));
const VideoRecommend = dynamic(() => import('./video-recommend'));

export default function RightSide() {
  return (
    <Box className="col-span-1">
      <Flex direction="column" gap="6">
        <VideoAggregate />
        <VideoRecommend />
      </Flex>
    </Box>
  );
}
