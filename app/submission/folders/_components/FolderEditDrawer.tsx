'use client';

import React from 'react';
import {
  Drawer,
  Portal,
  Stack,
  Field,
  Input,
  Textarea,
  Button,
  Box,
  Text,
  Grid,
  RadioGroup,
} from '@chakra-ui/react';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { object, string } from 'yup';
import { AiOutlineFolderOpen } from 'react-icons/ai';
import { Folder, FolderFormData } from './types';

// 组件 Props 类型
interface FolderEditDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  folder?: Folder | null;
  onSave: (data: Omit<Folder, 'id' | 'createdAt' | 'updatedAt'>) => void;
}

// 颜色选项
const colorOptions = [
  { value: 'gray', label: '灰色', color: 'gray.500' },
  { value: 'blue', label: '蓝色', color: 'blue.500' },
  { value: 'green', label: '绿色', color: 'green.500' },
  { value: 'purple', label: '紫色', color: 'purple.500' },
  { value: 'orange', label: '橙色', color: 'orange.500' },
  { value: 'pink', label: '粉色', color: 'pink.500' },
  { value: 'red', label: '红色', color: 'red.500' },
  { value: 'teal', label: '青色', color: 'teal.500' },
];

// 表单验证 schema
const schema = object({
  name: string()
    .required('请输入文件夹名称')
    .min(1, '文件夹名称不能为空')
    .max(20, '文件夹名称不能超过20个字符'),
  description: string()
    .required('请输入文件夹描述')
    .min(1, '文件夹描述不能为空')
    .max(100, '文件夹描述不能超过100个字符'),
  color: string().required('请选择文件夹颜色'),
});

export default function FolderEditDrawer({
  isOpen,
  onClose,
  folder,
  onSave,
}: FolderEditDrawerProps) {
  const isEditing = !!folder;

  // 表单配置
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<FolderFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      name: folder?.name || '',
      description: folder?.description || '',
      color: folder?.color || 'blue',
    },
  });

  // 处理保存
  const handleSave = (data: FolderFormData) => {
    onSave({
      ...data,
      videoCount: folder?.videoCount || 0,
      articleCount: folder?.articleCount || 0,
    });
    handleClose();
  };

  // 处理关闭
  const handleClose = () => {
    reset();
    onClose();
  };

  // 当 folder 改变时重置表单
  React.useEffect(() => {
    if (isOpen) {
      reset({
        name: folder?.name || '',
        description: folder?.description || '',
        color: folder?.color || 'blue',
      });
    }
  }, [folder, isOpen, reset]);

  return (
    <Drawer.Root
      open={isOpen}
      onOpenChange={(e) => !e.open && handleClose()}
      size="md"
    >
      <Portal>
        <Drawer.Backdrop />
        <Drawer.Positioner>
          <Drawer.Content>
            <Drawer.Header>
              <Drawer.Title>
                {isEditing ? '编辑文件夹' : '新建文件夹'}
              </Drawer.Title>
              <Drawer.CloseTrigger />
            </Drawer.Header>

            <Drawer.Body>
              <form id="folder-form" onSubmit={handleSubmit(handleSave)}>
                <Stack gap={6}>
                  {/* 文件夹名称 */}
                  <Field.Root invalid={!!errors.name} required>
                    <Field.Label>
                      <Field.RequiredIndicator />
                      文件夹名称
                    </Field.Label>
                    <Input
                      placeholder="请输入文件夹名称"
                      {...register('name')}
                    />
                    <Field.ErrorText>{errors.name?.message}</Field.ErrorText>
                    <Field.HelperText>
                      文件夹名称将用于内容分类，建议使用简洁明了的名称
                    </Field.HelperText>
                  </Field.Root>

                  {/* 文件夹描述 */}
                  <Field.Root invalid={!!errors.description} required>
                    <Field.Label>
                      <Field.RequiredIndicator />
                      文件夹描述
                    </Field.Label>
                    <Textarea
                      placeholder="请输入文件夹描述"
                      rows={3}
                      {...register('description')}
                    />
                    <Field.ErrorText>
                      {errors.description?.message}
                    </Field.ErrorText>
                    <Field.HelperText>
                      详细描述文件夹的用途和包含的内容类型
                    </Field.HelperText>
                  </Field.Root>

                  {/* 文件夹颜色 */}
                  <Field.Root invalid={!!errors.color} required>
                    <Field.Label>
                      <Field.RequiredIndicator />
                      文件夹颜色
                    </Field.Label>
                    <Controller
                      name="color"
                      control={control}
                      render={({ field }) => (
                        <RadioGroup.Root
                          value={field.value}
                          onValueChange={({ value }) => field.onChange(value)}
                        >
                          <Grid templateColumns="repeat(4, 1fr)" gap={3}>
                            {colorOptions.map((option) => (
                              <RadioGroup.Item
                                key={option.value}
                                value={option.value}
                                cursor="pointer"
                              >
                                <Box
                                  p={3}
                                  borderRadius="md"
                                  border="2px solid"
                                  borderColor={
                                    field.value === option.value
                                      ? option.color
                                      : 'gray.200'
                                  }
                                  bg={
                                    field.value === option.value
                                      ? `${option.value}.50`
                                      : 'white'
                                  }
                                  textAlign="center"
                                  transition="all 0.2s"
                                  _hover={{
                                    borderColor: option.color,
                                    bg: `${option.value}.50`,
                                  }}
                                >
                                  <RadioGroup.ItemHiddenInput />
                                  <Box
                                    mb={2}
                                    color={option.color}
                                    display="flex"
                                    justifyContent="center"
                                  >
                                    <AiOutlineFolderOpen size={20} />
                                  </Box>
                                  <Text fontSize="xs" fontWeight="medium">
                                    {option.label}
                                  </Text>
                                </Box>
                              </RadioGroup.Item>
                            ))}
                          </Grid>
                        </RadioGroup.Root>
                      )}
                    />
                    <Field.ErrorText>{errors.color?.message}</Field.ErrorText>
                    <Field.HelperText>
                      选择一个颜色来区分不同的文件夹
                    </Field.HelperText>
                  </Field.Root>

                  {/* 预览 */}
                  <Box>
                    <Text fontSize="sm" fontWeight="medium" mb={3}>
                      预览效果
                    </Text>
                    <Controller
                      name="color"
                      control={control}
                      render={({ field }) => (
                        <Box
                          p={4}
                          borderRadius="md"
                          border="1px solid"
                          borderColor="gray.200"
                          bg="gray.50"
                        >
                          <Box
                            display="inline-flex"
                            align="center"
                            gap={2}
                            p={2}
                            borderRadius="md"
                            bg={`${field.value}.100`}
                            color={`${field.value}.600`}
                          >
                            <AiOutlineFolderOpen size={20} />
                            <Text fontSize="sm" fontWeight="medium">
                              {register('name').name
                                ? document
                                    .getElementById('folder-form')
                                    ?.querySelector('input[name="name"]')
                                    ?.value || '文件夹名称'
                                : '文件夹名称'}
                            </Text>
                          </Box>
                        </Box>
                      )}
                    />
                  </Box>
                </Stack>
              </form>
            </Drawer.Body>

            {/* 底部按钮 */}
            <Drawer.Footer>
              <Stack direction="row" gap={3} width="100%">
                <Button variant="outline" onClick={handleClose} flex={1}>
                  取消
                </Button>
                <Button
                  type="submit"
                  form="folder-form"
                  colorScheme="blue"
                  isLoading={isSubmitting}
                  flex={1}
                >
                  {isEditing ? '更新' : '创建'}
                </Button>
              </Stack>
            </Drawer.Footer>
          </Drawer.Content>
        </Drawer.Positioner>
      </Portal>
    </Drawer.Root>
  );
}
