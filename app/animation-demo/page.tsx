'use client';
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Box,
  Button,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  SimpleGrid,
} from '@chakra-ui/react';
import Link from 'next/link';
import Content from '../_components/content';

// 动画示例组件
const AnimationExample = ({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) => {
  return (
    <Box p={6} borderRadius="lg" borderWidth="1px" shadow="md" h="100%">
      <Heading size="md" mb={4}>
        {title}
      </Heading>
      {children}
    </Box>
  );
};

export default function AnimationDemo() {
  const [isAnimating, setIsAnimating] = useState(false);

  return (
    <Content>
      <Container maxW="container.xl" py={8}>
        <VStack gap={8} align="stretch">
          <Box textAlign="center">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Heading mb={2}>动画效果演示</Heading>
              <Text fontSize="lg" color="gray.500">
                这个页面展示了各种动画效果，包括页面切换动画
              </Text>
            </motion.div>
          </Box>

          <HStack justify="center" gap={4} mb={8}>
            <Link href="/">返回首页</Link>
            <Link href="/articles">查看文章页</Link>
          </HStack>

          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} gap={6}>
            {/* 示例1：淡入淡出 */}
            <AnimationExample title="淡入淡出动画">
              <motion.div
                animate={{ opacity: isAnimating ? [0, 1, 0, 1] : 1 }}
                transition={{ duration: 2, repeat: isAnimating ? Infinity : 0 }}
              >
                <Box
                  bg="blue.500"
                  h="100px"
                  borderRadius="md"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Text color="white" fontWeight="bold">
                    淡入淡出效果
                  </Text>
                </Box>
              </motion.div>
              <Button
                mt={4}
                onClick={() => setIsAnimating(!isAnimating)}
                size="sm"
                colorScheme="blue"
              >
                {isAnimating ? '停止动画' : '开始动画'}
              </Button>
            </AnimationExample>

            {/* 示例2：缩放 */}
            <AnimationExample title="缩放动画">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Box
                  bg="green.500"
                  h="100px"
                  borderRadius="md"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  cursor="pointer"
                >
                  <Text color="white" fontWeight="bold">
                    悬停或点击我
                  </Text>
                </Box>
              </motion.div>
            </AnimationExample>

            {/* 示例3：旋转 */}
            <AnimationExample title="旋转动画">
              <motion.div
                animate={{ rotate: isAnimating ? 360 : 0 }}
                transition={{
                  duration: 2,
                  repeat: isAnimating ? Infinity : 0,
                  ease: 'linear',
                }}
              >
                <Box
                  bg="purple.500"
                  h="100px"
                  w="100px"
                  mx="auto"
                  borderRadius="md"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Text color="white" fontWeight="bold">
                    旋转效果
                  </Text>
                </Box>
              </motion.div>
              <Button
                mt={4}
                onClick={() => setIsAnimating(!isAnimating)}
                size="sm"
                colorScheme="purple"
              >
                {isAnimating ? '停止动画' : '开始动画'}
              </Button>
            </AnimationExample>

            {/* 示例4：弹跳 */}
            <AnimationExample title="弹跳动画">
              <motion.div
                animate={{ y: isAnimating ? [0, -30, 0] : 0 }}
                transition={{
                  duration: 0.8,
                  repeat: isAnimating ? Infinity : 0,
                  ease: 'easeInOut',
                }}
              >
                <Box
                  bg="orange.500"
                  h="100px"
                  w="100px"
                  mx="auto"
                  borderRadius="full"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Text color="white" fontWeight="bold">
                    弹跳效果
                  </Text>
                </Box>
              </motion.div>
              <Button
                mt={4}
                onClick={() => setIsAnimating(!isAnimating)}
                size="sm"
                colorScheme="orange"
              >
                {isAnimating ? '停止动画' : '开始动画'}
              </Button>
            </AnimationExample>

            {/* 示例5：颜色变化 */}
            <AnimationExample title="颜色变化动画">
              <motion.div
                animate={{
                  backgroundColor: isAnimating
                    ? ['#4299E1', '#48BB78', '#ED8936', '#9F7AEA', '#4299E1']
                    : '#4299E1',
                }}
                transition={{
                  duration: 4,
                  repeat: isAnimating ? Infinity : 0,
                  ease: 'linear',
                }}
                style={{ borderRadius: '0.375rem' }}
              >
                <Box
                  h="100px"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Text color="white" fontWeight="bold">
                    颜色变化效果
                  </Text>
                </Box>
              </motion.div>
              <Button
                mt={4}
                onClick={() => setIsAnimating(!isAnimating)}
                size="sm"
                colorScheme="teal"
              >
                {isAnimating ? '停止动画' : '开始动画'}
              </Button>
            </AnimationExample>

            {/* 示例6：路径动画 */}
            <AnimationExample title="路径动画">
              <Box position="relative" h="100px">
                <motion.div
                  animate={{
                    x: isAnimating ? [0, 100, 100, 0, 0] : 0,
                    y: isAnimating ? [0, 0, 50, 50, 0] : 0,
                  }}
                  transition={{
                    duration: 3,
                    repeat: isAnimating ? Infinity : 0,
                    ease: 'linear',
                  }}
                  style={{
                    width: '30px',
                    height: '30px',
                    borderRadius: '50%',
                    backgroundColor: '#E53E3E',
                    position: 'absolute',
                  }}
                />
              </Box>
              <Button
                mt={4}
                onClick={() => setIsAnimating(!isAnimating)}
                size="sm"
                colorScheme="red"
              >
                {isAnimating ? '停止动画' : '开始动画'}
              </Button>
            </AnimationExample>
          </SimpleGrid>

          <Box textAlign="center" mt={8}>
            <Text fontSize="sm" color="gray.500">
              尝试在不同页面之间导航，体验页面切换动画效果
            </Text>
          </Box>
        </VStack>
      </Container>
    </Content>
  );
}
