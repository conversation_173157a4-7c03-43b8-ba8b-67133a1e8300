import { Box } from '@chakra-ui/react';
import SteamHeader from '../_components/header';

export async function generateMetadata() {
  return {
    title: '极致创思聚合网站|scratch编程',
    content: '视频,文章,scratch编程网站聚合',
    csp: "default-src 'self'; script-src 'self' 'unsafe-inline';",
  };
}

export default function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <Box
      w="100%"
      className="bg-background text-foreground font-noto flex min-h-screen flex-col"
    >
      <SteamHeader />
      {children}
    </Box>
  );
}
