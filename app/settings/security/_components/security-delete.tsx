'use client';
import {
  Card,
  Field,
  Input,
  Text,
  Button,
  Box,
  Stack,
  Heading,
} from '@chakra-ui/react';

export default function SecurityDelete() {
  return (
    <Card.Root variant={'elevated'} mb={6}>
      <Card.Body>
        <form>
          <Stack gap={4}>
            <Box>
              <Heading as="h1" size="lg">
                删除账户
              </Heading>
              <Text>删除账户将永久删除您的所有数据。请谨慎操作。</Text>
            </Box>

            <Field.Root>
              <Field.Label>请输入您的账户密码以确认删除</Field.Label>
              <Input type="password" placeholder="输入密码" />
            </Field.Root>

            <Button variant={'outline'} color={'red.600'} type="submit">
              删除账户
            </Button>
          </Stack>
        </form>
      </Card.Body>
    </Card.Root>
  );
}
