# 视频编辑抽屉组件 (VideoEditDrawer)

一个功能完整的视频编辑抽屉组件，基于 Chakra UI 构建，支持视频信息编辑、封面修改、文件夹选择等功能。

## 特性

- 🎬 **视频封面展示与修改** - 支持查看和更换视频封面图片
- ✏️ **视频信息编辑** - 编辑视频标题和描述
- 📁 **文件夹选择** - 将视频移动到指定文件夹
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🔧 **表单验证** - 完整的表单验证和错误提示
- 🎨 **主题支持** - 支持明暗主题切换
- 📝 **TypeScript 支持** - 完整的类型定义

## 安装

确保已安装以下依赖：

```bash
npm install @chakra-ui/react @emotion/react @emotion/styled framer-motion react-hook-form @hookform/resolvers yup chakra-react-select react-icons
```

## 基本使用

```tsx
import { VideoEditDrawer, useVideoEditDrawer } from '@/components/ui/video-edit-drawer';

function MyComponent() {
  const { isOpen, open, close } = useVideoEditDrawer();

  const handleSave = (data) => {
    console.log('保存:', data);
    close();
  };

  const handlePublish = (data) => {
    console.log('发布:', data);
    close();
  };

  return (
    <>
      <Button onClick={open}>编辑视频</Button>
      
      <VideoEditDrawer
        isOpen={isOpen}
        onClose={close}
        onSave={handleSave}
        onPublish={handlePublish}
      />
    </>
  );
}
```

## 完整示例

```tsx
import { VideoEditDrawer, useVideoEditDrawer } from '@/components/ui/video-edit-drawer';
import type { VideoData, FolderOption } from '@/components/ui/video-edit-drawer';

const videoData: VideoData = {
  id: '1',
  title: '我的视频',
  description: '视频描述',
  coverImage: '/path/to/cover.jpg',
  folderId: 'default',
};

const folders: FolderOption[] = [
  { value: 'default', label: '默认文件夹' },
  { value: 'education', label: '教育' },
  { value: 'entertainment', label: '娱乐' },
];

function VideoEditExample() {
  const { isOpen, open, close } = useVideoEditDrawer();

  const handleSave = async (data) => {
    // 调用 API 保存数据
    await saveVideo(data);
    close();
  };

  const handlePublish = async (data) => {
    // 调用 API 发布视频
    await publishVideo(data);
    close();
  };

  return (
    <>
      <Button onClick={open}>编辑视频</Button>
      
      <VideoEditDrawer
        isOpen={isOpen}
        onClose={close}
        videoData={videoData}
        folders={folders}
        onSave={handleSave}
        onPublish={handlePublish}
      />
    </>
  );
}
```

## API 参考

### VideoEditDrawer Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `isOpen` | `boolean` | - | 控制抽屉是否打开 |
| `onClose` | `() => void` | - | 关闭抽屉的回调函数 |
| `videoData` | `VideoData` | - | 视频数据对象 |
| `onSave` | `(data: VideoFormData) => void` | - | 保存按钮点击回调 |
| `onPublish` | `(data: VideoFormData) => void` | - | 发布按钮点击回调 |
| `folders` | `FolderOption[]` | 默认文件夹列表 | 可选择的文件夹列表 |
| `isLoading` | `boolean` | `false` | 是否显示加载状态 |

### 数据类型

```typescript
interface VideoData {
  id: string;
  title: string;
  description: string;
  coverImage: string;
  folderId: string;
  createdAt?: string;
  updatedAt?: string;
  duration?: number;
  size?: number;
  format?: string;
}

interface FolderOption {
  value: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
}

interface VideoFormData {
  title: string;
  description: string;
  folderId: string;
  coverImage?: File;
}
```

### useVideoEditDrawer Hook

```typescript
const { isOpen, open, close, toggle } = useVideoEditDrawer();
```

返回值：
- `isOpen`: 抽屉是否打开
- `open`: 打开抽屉
- `close`: 关闭抽屉
- `toggle`: 切换抽屉状态

## 自定义样式

组件使用 Chakra UI 构建，支持通过 Chakra UI 的主题系统进行样式定制。

## 注意事项

1. 确保在使用前已正确配置 Chakra UI Provider
2. 文件上传功能需要配合后端 API 实现
3. 表单验证使用 yup，可根据需要自定义验证规则
4. 组件支持服务端渲染 (SSR)

## 示例页面

查看 `VideoEditDrawerExample.tsx` 文件获取完整的使用示例。
