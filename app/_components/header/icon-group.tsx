import { memo } from 'react';
import { Flex, IconButton, Float, Circle } from '@chakra-ui/react';
import { CiSun, CiBellOn } from 'react-icons/ci';
import { IoSettingsOutline } from 'react-icons/io5';
import React from 'react';

interface IProps {
  notificationState: boolean;
  handleNotificationState: (state: boolean) => void;
  handleNavigation: (path: string) => void;
}

/**
 * @description 头部栏图标组组件，包含主题切换、消息通知和设置按钮
 * @param notificationState - 通知状态
 * @param handleNotificationState - 处理通知状态变化的回调函数
 * @param handleNavigation - 处理导航跳转的回调函数
 */
const IconGroup = ({
  notificationState,
  handleNotificationState,
  handleNavigation,
}: IProps) => {
  /**
   * @description header 栏icon图标组 切换主题 消息通知 settings 设置
   */

  return (
    <Flex gap={2}>
      <IconButton variant="ghost">
        <CiSun />
      </IconButton>
      <IconButton
        variant="ghost"
        position="relative"
        onClick={() => handleNotificationState(!notificationState)}
      >
        <CiBellOn />
        <Float offset="1">
          <Circle size="1" bg="red" color="white"></Circle>
        </Float>
      </IconButton>
      <IconButton
        variant="ghost"
        color={'primary'}
        animation="spin 3s linear infinite"
        _hover={{
          animation: 'none',
          transform: 'rotate(0deg)',
          transition: 'transform 0.3s ease-out',
        }}
        onClick={() => handleNavigation('/settings/profile')}
      >
        <IoSettingsOutline />
      </IconButton>
    </Flex>
  );
};

export default memo(IconGroup);
