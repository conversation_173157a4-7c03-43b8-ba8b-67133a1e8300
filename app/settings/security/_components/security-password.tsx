'use client';
import {
  Card,
  Heading,
  Text,
  Stack,
  Box,
  Field,
  Button,
  Input,
} from '@chakra-ui/react';

export default function SecurityPassword() {
  return (
    <Card.Root variant={'elevated'}>
      <Card.Body>
        <Stack>
          <Box>
            <Heading as="h1" size="lg">
              修改密码
            </Heading>
            <Text>在这里您可以修改您的账户密码。</Text>
          </Box>
          <Box>
            <form>
              <Stack gap={4}>
                <Field.Root>
                  <Field.Label>当前密码</Field.Label>
                  <Input type="password" placeholder="输入当前密码" />
                </Field.Root>

                <Field.Root>
                  <Field.Label>新密码</Field.Label>
                  <Input type="password" placeholder="输入新密码" />
                </Field.Root>

                <Field.Root>
                  <Field.Label>确认新密码</Field.Label>
                  <Input type="password" placeholder="确认新密码" />
                </Field.Root>

                <Button variant={'outline'} color={'blue.600'} type="submit">
                  修改密码
                </Button>
              </Stack>
            </form>
          </Box>
        </Stack>

        {/* 其他密码修改相关内容 */}
      </Card.Body>
    </Card.Root>
  );
}
