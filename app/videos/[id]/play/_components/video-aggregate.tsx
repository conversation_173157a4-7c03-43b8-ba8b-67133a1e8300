'use client';
import { useState } from 'react';
import { Card, Stack, Heading, List } from '@chakra-ui/react';
import Link from 'next/link';
import { AiOutlineVideoCamera, AiTwotoneCustomerService } from 'react-icons/ai';

export default function VideoAggregate() {
  /**
   * @description 视频播放集合列表
   */
  const [count, setCount] = useState(20);
  return (
    <Card.Root
      className="overflow-hidden overflow-y-auto shadow-md"
      maxHeight={'350px'}
    >
      <Card.Body>
        <Stack gap={4}>
          <Heading as="h3" size="md" className="tracking-wide">
            视频播放列表
          </Heading>
          <List.Root gap="2" variant="plain" align="center">
            {Array.from({ length: count }).map((_, index) => (
              <List.Item
                className="hover:!text-primary cursor-pointer hover:bg-gray-100"
                key={index}
              >
                <List.Indicator asChild>
                  {/**
                   * TODO: 当前播放 AiTwotoneCustomerService
                   */}
                  <AiOutlineVideoCamera />
                </List.Indicator>
                <Link href="#">视频播放1</Link>
              </List.Item>
            ))}
          </List.Root>
        </Stack>
      </Card.Body>
    </Card.Root>
  );
}
