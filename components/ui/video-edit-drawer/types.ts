// 视频数据类型定义
export interface VideoData {
  id: string;
  title: string;
  description: string;
  coverImage: string;
  folderId: string;
  createdAt?: string;
  updatedAt?: string;
  duration?: number;
  size?: number;
  format?: string;
}

// 文件夹选项类型
export interface FolderOption {
  value: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
}

// 表单数据类型
export interface VideoFormData {
  title: string;
  description: string;
  folderId: string;
  coverImage?: File;
}

// 组件 Props 类型
export interface VideoEditDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  videoData?: VideoData;
  onSave: (data: VideoFormData) => void;
  onPublish: (data: VideoFormData) => void;
  folders?: FolderOption[];
  isLoading?: boolean;
}

// Hook 返回类型
export interface UseVideoEditDrawer {
  isOpen: boolean;
  open: () => void;
  close: () => void;
  toggle: () => void;
}
