'use client';

import React, { useState } from 'react';
import {
  Dialog,
  Portal,
  Stack,
  Button,
  Box,
  Text,
  Flex,
  Image,
  AspectRatio,
  RadioGroup,
  Grid,
} from '@chakra-ui/react';
import { AiOutlineFolderOpen, AiOutlinePlayCircle } from 'react-icons/ai';
import { Video, Folder } from './types';

// 组件 Props 类型
interface VideoMoveModalProps {
  isOpen: boolean;
  onClose: () => void;
  video: Video | null;
  onConfirm: (videoId: string, targetFolderId: string) => void;
}

// 模拟文件夹数据
const mockFolders: Folder[] = [
  {
    id: '1',
    name: '默认文件夹',
    description: '系统默认文件夹，用于存放未分类的内容',
    color: 'gray',
    videoCount: 12,
    createdAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: '教育学习',
    description: '教育相关的视频，包括教程、课程等内容',
    color: 'blue',
    videoCount: 25,
    createdAt: '2024-01-05T10:30:00Z',
  },
  {
    id: '3',
    name: '生活方式',
    description: '生活、美食、旅行等日常生活相关内容',
    color: 'green',
    videoCount: 18,
    createdAt: '2024-01-08T16:45:00Z',
  },
  {
    id: '4',
    name: '科技数码',
    description: '科技产品评测、技术分享、数码资讯',
    color: 'purple',
    videoCount: 30,
    createdAt: '2024-01-12T09:15:00Z',
  },
];

// 格式化时长
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

export default function VideoMoveModal({
  isOpen,
  onClose,
  video,
  onConfirm,
}: VideoMoveModalProps) {
  const [selectedFolderId, setSelectedFolderId] = useState<string>('');

  // 处理确认移动
  const handleConfirm = () => {
    if (video && selectedFolderId && selectedFolderId !== video.folderId) {
      onConfirm(video.id, selectedFolderId);
    }
  };

  // 处理关闭
  const handleClose = () => {
    setSelectedFolderId('');
    onClose();
  };

  // 重置选择
  React.useEffect(() => {
    if (isOpen) {
      setSelectedFolderId('');
    }
  }, [isOpen]);

  if (!video) return null;

  // 过滤掉当前文件夹
  const availableFolders = mockFolders.filter(
    (folder) => folder.id !== video.folderId
  );

  return (
    <Dialog.Root open={isOpen} onOpenChange={(e) => !e.open && handleClose()}>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content maxW="2xl">
            <Dialog.Header>
              <Dialog.Title>移动视频</Dialog.Title>
              <Dialog.CloseTrigger />
            </Dialog.Header>

            <Dialog.Body>
              <Stack gap={6}>
                {/* 视频信息 */}
                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={3}>
                    要移动的视频
                  </Text>
                  <Box
                    p={4}
                    borderRadius="md"
                    border="1px solid"
                    borderColor="gray.200"
                    bg="gray.50"
                  >
                    <Flex gap={3}>
                      <Box width="120px" flexShrink={0} position="relative">
                        <AspectRatio ratio={16 / 9}>
                          <Image
                            src={video.coverImage}
                            alt={video.title}
                            borderRadius="md"
                            objectFit="cover"
                          />
                        </AspectRatio>
                        <Flex
                          position="absolute"
                          bottom={1}
                          right={1}
                          bg="blackAlpha.700"
                          color="white"
                          px={1}
                          py={0.5}
                          borderRadius="sm"
                          fontSize="xs"
                          align="center"
                          gap={1}
                        >
                          <AiOutlinePlayCircle size={10} />
                          <Text>{formatDuration(video.duration)}</Text>
                        </Flex>
                      </Box>
                      <Box flex={1}>
                        <Text fontWeight="medium" fontSize="sm" mb={1}>
                          {video.title}
                        </Text>
                        <Text fontSize="xs" color="gray.600" noOfLines={2}>
                          {video.description}
                        </Text>
                      </Box>
                    </Flex>
                  </Box>
                </Box>

                {/* 目标文件夹选择 */}
                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={3}>
                    选择目标文件夹
                  </Text>

                  {availableFolders.length === 0 ? (
                    <Box textAlign="center" py={8}>
                      <Text color="gray.500" fontSize="sm">
                        没有其他可用的文件夹
                      </Text>
                    </Box>
                  ) : (
                    <RadioGroup.Root
                      value={selectedFolderId}
                      onValueChange={({ value }) => setSelectedFolderId(value)}
                    >
                      <Grid
                        templateColumns="repeat(auto-fit, minmax(250px, 1fr))"
                        gap={3}
                      >
                        {availableFolders.map((folder) => (
                          <RadioGroup.Item
                            key={folder.id}
                            value={folder.id}
                            cursor="pointer"
                          >
                            <Box
                              p={3}
                              borderRadius="md"
                              border="2px solid"
                              borderColor={
                                selectedFolderId === folder.id
                                  ? `${folder.color}.500`
                                  : 'gray.200'
                              }
                              bg={
                                selectedFolderId === folder.id
                                  ? `${folder.color}.50`
                                  : 'white'
                              }
                              transition="all 0.2s"
                              _hover={{
                                borderColor: `${folder.color}.500`,
                                bg: `${folder.color}.50`,
                              }}
                            >
                              <RadioGroup.ItemHiddenInput />
                              <Flex align="center" gap={3}>
                                <Box
                                  p={2}
                                  borderRadius="md"
                                  bg={`${folder.color}.100`}
                                  color={`${folder.color}.600`}
                                >
                                  <AiOutlineFolderOpen size={20} />
                                </Box>
                                <Box flex={1}>
                                  <Text
                                    fontWeight="medium"
                                    fontSize="sm"
                                    mb={1}
                                  >
                                    {folder.name}
                                  </Text>
                                  <Text
                                    fontSize="xs"
                                    color="gray.600"
                                    noOfLines={2}
                                  >
                                    {folder.description}
                                  </Text>
                                  <Text fontSize="xs" color="gray.500" mt={1}>
                                    {folder.videoCount} 个视频
                                  </Text>
                                </Box>
                              </Flex>
                            </Box>
                          </RadioGroup.Item>
                        ))}
                      </Grid>
                    </RadioGroup.Root>
                  )}
                </Box>

                {/* 移动说明 */}
                {selectedFolderId && (
                  <Box
                    p={3}
                    borderRadius="md"
                    bg="blue.50"
                    border="1px solid"
                    borderColor="blue.200"
                  >
                    <Text fontSize="sm" color="blue.700">
                      <Text as="span" fontWeight="medium">
                        提示：
                      </Text>
                      视频将从当前文件夹移动到选定的目标文件夹，此操作不会影响视频文件本身。
                    </Text>
                  </Box>
                )}
              </Stack>
            </Dialog.Body>

            <Dialog.Footer>
              <Stack direction="row" gap={3} width="100%">
                <Button variant="outline" onClick={handleClose} flex={1}>
                  取消
                </Button>
                <Button
                  colorScheme="blue"
                  onClick={handleConfirm}
                  isDisabled={
                    !selectedFolderId || availableFolders.length === 0
                  }
                  flex={1}
                >
                  确认移动
                </Button>
              </Stack>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
