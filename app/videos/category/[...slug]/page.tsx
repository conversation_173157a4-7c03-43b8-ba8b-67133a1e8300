import dynamic from 'next/dynamic';
import { Container } from '@chakra-ui/react';
import Content from '../../../_components/content';

const TopSide = dynamic(() => import('../../_components/top-side'));
const VideoContent = dynamic(() => import('../../_components/video-content'));
const VideoTitle = dynamic(() => import('../_components/video-title'));

export default function Page() {
  /**
   * @description 视频分类展示页面
   */
  return (
    <Content>
      <Container
        maxW="100%"
        fluid
        className="flex h-full flex-col gap-6 overflow-y-scroll scroll-smooth"
      >
        <TopSide />
        <VideoTitle />
        <VideoContent />
      </Container>
    </Content>
  );
}
