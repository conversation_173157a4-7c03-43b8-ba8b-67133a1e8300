'use client';
import { memo } from 'react';
import { Popover, Portal, IconButton } from '@chakra-ui/react';
import { AiOutlineSmile } from 'react-icons/ai';
import dynamic from 'next/dynamic';
import data from '@emoji-mart/data';
const EmojiPicker = dynamic(() => import('@emoji-mart/react'), { ssr: false });

interface IProps {
  handleEmojiSelect: (emoji: unknown) => void;
}

const PostsEmoji = ({ handleEmojiSelect }: IProps) => {
  return (
    <Popover.Root lazyMount unmountOnExit>
      <Popover.Trigger asChild>
        <IconButton bg="white" variant="ghost" size="sm" aria-label="添加表情">
          <AiOutlineSmile />
        </IconButton>
      </Popover.Trigger>
      <Portal>
        <Popover.Positioner>
          <Popover.Content>
            <EmojiPicker
              data={data}
              onEmojiSelect={handleEmojiSelect}
              locale="zh"
            />
          </Popover.Content>
        </Popover.Positioner>
      </Portal>
    </Popover.Root>
  );
};
export default memo(PostsEmoji);
