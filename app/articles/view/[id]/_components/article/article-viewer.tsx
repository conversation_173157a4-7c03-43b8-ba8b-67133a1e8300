'use client';
import { Viewer } from '@bytemd/react';
import gfm from '@bytemd/plugin-gfm';
import highlight from '@bytemd/plugin-highlight';
import 'github-markdown-css/github-markdown-light.css';
import 'bytemd/dist/index.css';
import 'highlight.js/styles/vs.css';

interface Props {
  value?: string;
}

const plugins = [gfm(), highlight()];

export default function ArticleMDX() {
  const markdown = `
    React Hooks 介绍与用例
React Hooks 是 React 16.8 引入的功能，允许函数组件使用状态、生命周期等特性，而无需编写类组件。Hooks 使代码更简洁、可复用，解决了类组件中逻辑分散、复用困难等问题。以下介绍常用 Hooks，并通过实际用例展示其用法。

1. useState - 状态管理
useState 用于在函数组件中添加状态，返回状态值和更新函数。
用例：计数器
创建一个简单的计数器，点击按钮增加或减少计数值。
import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);

  return (
    <div>
      <h2>Count: {count}</h2>
      <button onClick={() => setCount(count + 1)}>Add</button>
      <button onClick={() => setCount(count - 1)}>Subtract</button>
    </div>
  );
}

说明

useState(0) 初始化状态 count 为 0。
setCount 更新状态，触发组件重新渲染。
测试点：在 Markdown 编辑器中，检查代码块是否高亮，标题是否正确显示。


2. useEffect - 副作用处理
useEffect 用于处理副作用，如数据获取、订阅等，在组件渲染后执行。
用例：获取用户数据
从 API 获取用户数据并显示。
import React, { useState, useEffect } from 'react';

function UserProfile() {
  const [user, setUser] = useState(null);

  useEffect(() => {
    fetch('https://api.example.com/user')
      .then(res => res.json())
      .then(data => setUser(data))
      .catch(err => console.error('Error:', err));
    // 清理函数（可选）
    return () => console.log('Cleanup');
  }, []); // 空数组表示仅在挂载时运行

  return (
    <div>
      {user ? (
        <p>User: {user.name}</p>
      ) : (
        <p>Loading...</p>
      )}
    </div>
  );
}

说明

useEffect 在组件挂载后获取数据。
依赖数组 [] 确保只运行一次，类似 componentDidMount。
测试点：检查代码块中的注释和 JSX 语法是否正确高亮，列表和段落是否渲染清晰。


3. useContext - 上下文管理
useContext 用于访问 React Context，避免 props 层层传递。
用例：主题切换
在应用中共享主题设置。
import React, { useContext, createContext } from 'react';

const ThemeContext = createContext('light');

function App() {
  return (
    <ThemeContext.Provider value="dark">
      <ThemeButton />
    </ThemeContext.Provider>
  );
}

function ThemeButton() {
  const theme = useContext(ThemeContext);
  return <button style={{ background: theme === 'dark' ? '#333' : '#FFF' }}>
    Theme: {theme}
  </button>;
}

说明

createContext 创建上下文，useContext 获取当前值。
适合全局状态管理，如主题或用户认证。
测试点：验证内联样式 {} 和三元运算符是否在代码块中清晰显示。


4. useReducer - 复杂状态管理
useReducer 适合管理复杂状态逻辑，类似 Redux 的 reducer 模式。
用例：Todo 列表
实现一个简单的 Todo 列表，添加和删除任务。
import React, { useReducer } from 'react';

const initialState = { todos: [] };

function reducer(state, action) {
  switch (action.type) {
    case 'add':
      return { todos: [...state.todos, action.payload] };
    case 'remove':
      return { todos: state.todos.filter((_, i) => i !== action.index) };
    default:
      return state;
  }
}

function TodoList() {
  const [state, dispatch] = useReducer(reducer, initialState);
  const [input, setInput] = useState('');

  const addTodo = () => {
    dispatch({ type: 'add', payload: input });
    setInput('');
  };

  return (
    <div>
      <input value={input} onChange={e => setInput(e.target.value)} />
      <button onClick={addTodo}>Add Todo</button>
      <ul>
        {state.todos.map((todo, index) => (
          <li key={index}>
            {todo} <button onClick={() => dispatch({ type: 'remove', index })}>Remove</button>
          </li>
        ))}
      </ul>
    </div>
  );
}

说明

useReducer 管理 todos 数组，处理添加和删除操作。
适合复杂状态逻辑，代码更集中。
测试点：检查列表（<ul>、<li>）和事件处理（如 onClick）是否在编辑器中渲染清晰。


5. useCallback 和 useMemo - 性能优化
useCallback 和 useMemo 用于优化性能，避免不必要的函数或值重新创建。
用例：优化子组件渲染
防止子组件因父组件重新渲染而重复渲染。
import React, { useState, useCallback } from 'react';

function Parent() {
  const [count, setCount] = useState(0);

  const increment = useCallback(() => {
    setCount(c => c + 1);
  }, []); // 无依赖，函数只创建一次

  return (
    <div>
      <p>Count: {count}</p>
      <Child onIncrement={increment} />
    </div>
  );
}

function Child({ onIncrement }) {
  console.log('Child rendered');
  return <button onClick={onIncrement}>Increment</button>;
}

说明

useCallback 缓存 increment 函数，避免 Child 组件不必要地重新渲染。
useMemo 可用于缓存复杂计算结果（此处未展示，参考前文）。
测试点：检查代码块中箭头函数和 JSX 属性是否高亮，段落是否对齐。


6. useRef - 引用管理
useRef 创建可变引用，常用于访问 DOM 或保存跨渲染的值。
用例：聚焦输入框
点击按钮让输入框获得焦点。
import React, { useRef } from 'react';

function FocusInput() {
  const inputRef = useRef(null);

  const focus = () => {
    inputRef.current.focus();
  };

  return (
    <div>
      <input ref={inputRef} type="text" placeholder="Type here" />
      <button onClick={focus}>Focus Input</button>
    </div>
  );
}

说明

inputRef.current 引用 DOM 元素，调用其 focus 方法。
修改 .current 不会触发重新渲染。
测试点：验证 <input> 标签和 ref 属性是否在编辑器中正确显示。


7. 自定义 Hook - 逻辑复用
自定义 Hook 封装可复用逻辑，结合多个内置 Hook。
用例：监听窗口大小
创建一个 useWindowSize Hook，实时返回窗口尺寸。
import React, { useState, useEffect } from 'react';

function useWindowSize() {
  const [size, setSize] = useState({ width: window.innerWidth, height: window.innerHeight });

  useEffect(() => {
    const handleResize = () => setSize({ width: window.innerWidth, height: window.innerHeight });
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return size;
}

function WindowInfo() {
  const { width, height } = useWindowSize();
  return <p>Window: {width} x {height}</p>;
}

说明

useWindowSize 结合 useState 和 useEffect，监听窗口大小变化。
自定义 Hook 命名必须以 use 开头。
测试点：检查自定义 Hook 的代码块和返回值是否清晰，段落是否易读。


Hooks 使用规则

顶层调用：Hooks 只能在函数组件或自定义 Hook 的顶层调用，不能在循环或条件语句中。
仅限 React 函数：不要在普通 JavaScript 函数中调用 Hooks。
依赖数组：在 useEffect、useCallback 等中，依赖数组需包含所有相关变量。

测试点

验证无序列表（-）是否正确渲染为项目符号。
检查规则描述是否清晰，易于编辑。


总结
React Hooks 使函数组件更强大，取代类组件的复杂逻辑。useState 和 useEffect 是基础，useContext 和 useReducer 适合复杂场景，useCallback 和 useMemo 优化性能，useRef 和自定义 Hook 提供灵活性。
测试 Markdown 编辑器

渲染：复制此内容到编辑器，检查标题、代码块、列表、加粗等是否正确显示。
编辑：尝试修改代码或文本，测试实时预览或格式化功能。
高亮：验证 JSX 代码块是否支持语法高亮。
结构：检查多级标题、列表嵌套是否渲染清晰。




    `;

  return (
    <div className="md-viewer">
      <Viewer value={markdown} plugins={plugins} />
    </div>
  );
}
