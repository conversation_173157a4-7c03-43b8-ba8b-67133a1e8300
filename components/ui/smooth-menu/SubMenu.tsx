import { useState, useEffect } from 'react';
import { Box, HStack, Text } from '@chakra-ui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { MdChevronRight } from 'react-icons/md';
import { SubMenuProps } from './types';
import { MenuItem } from './MenuItem';
import {
  arrowVariants,
  menuItemHoverAnimation,
  subMenuVariants,
  subMenuContentVariants,
} from './animations';

const MotionBox = motion(Box);
const MotionChevron = motion(MdChevronRight);

export const SubMenu = ({
  item,
  isOpen,
  selectedKeys,
  onSelect,
  onOpenChange,
}: SubMenuProps) => {
  const [isHovered, setIsHovered] = useState(false);

  // 处理子菜单点击
  const handleTitleClick = () => {
    if (!item.disabled) {
      onOpenChange(item.key, !isOpen);
    }
  };

  // 当鼠标悬停时自动打开子菜单
  useEffect(() => {
    if (isHovered && !isOpen && !item.disabled) {
      const timer = setTimeout(() => {
        onOpenChange(item.key, true);
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [isHovered, isOpen, item.disabled, item.key, onOpenChange]);

  return (
    <Box as="li" role="none">
      {/* 子菜单标题 */}
      <MotionBox
        as="div"
        role="menuitem"
        aria-expanded={isOpen}
        aria-haspopup="true"
        {...menuItemHoverAnimation}
        px={4}
        py={2}
        cursor={item.disabled ? 'not-allowed' : 'pointer'}
        opacity={item.disabled ? 0.5 : 1}
        onClick={handleTitleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        userSelect="none"
      >
        <HStack gap={2} color={item.disabled ? 'gray.400' : 'inherit'}>
          {item.icon && <Box fontSize="1.1em">{item.icon}</Box>}
          <Text flex="1">{item.label}</Text>
          <MotionChevron
            variants={arrowVariants}
            animate={isOpen ? 'open' : 'closed'}
            fontSize="0.8em"
          />
        </HStack>
      </MotionBox>

      {/* 子菜单内容 */}
      <AnimatePresence initial={false}>
        {isOpen && (
          <MotionBox
            as="ul"
            role="menu"
            variants={subMenuVariants}
            initial="closed"
            animate="open"
            exit="closed"
            overflow="hidden"
            pl={4}
          >
            <MotionBox
              variants={subMenuContentVariants}
              initial="closed"
              animate="open"
              exit="closed"
            >
              {item.children?.map((childItem) =>
                childItem.children ? (
                  <SubMenu
                    key={childItem.key}
                    item={childItem}
                    isOpen={
                      selectedKeys.includes(childItem.key) ||
                      childItem.children.some((c) =>
                        selectedKeys.includes(c.key)
                      )
                    }
                    selectedKeys={selectedKeys}
                    onSelect={onSelect}
                    onOpenChange={onOpenChange}
                  />
                ) : (
                  <MenuItem
                    key={childItem.key}
                    item={childItem}
                    isSelected={selectedKeys.includes(childItem.key)}
                    onSelect={onSelect}
                  />
                )
              )}
            </MotionBox>
          </MotionBox>
        )}
      </AnimatePresence>
    </Box>
  );
};
