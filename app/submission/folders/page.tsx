'use client';

import {
  Container,
  Box,
  Heading,
  Text,
  Button,
  Stack,
  Grid,
  Card,
  IconButton,
  Flex,
  Badge,
  Menu,
  Portal,
} from '@chakra-ui/react';
import { useState } from 'react';
import {
  AiOutlineFolderOpen,
  AiOutlineEdit,
  AiOutlineDelete,
  AiOutlinePlus,
  AiOutlineMore,
  AiOutlineVideoCamera,
} from 'react-icons/ai';
import { toaster } from '@/components/ui/toaster';
import FolderEditDrawer from './_components/FolderEditDrawer';
import DeleteConfirmDialog from './_components/DeleteConfirmDialog';
import FolderDetailDrawer from './_components/FolderDetailDrawer';
import { Folder } from './_components/types';

// 模拟文件夹数据
const mockFolders: Folder[] = [
  {
    id: '1',
    name: '默认文件夹',
    description: '系统默认文件夹，用于存放未分类的内容',
    color: 'gray',
    videoCount: 12,
    createdAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: '教育学习',
    description: '教育相关的视频，包括教程、课程等内容',
    color: 'blue',
    videoCount: 25,
    createdAt: '2024-01-05T10:30:00Z',
    updatedAt: '2024-01-10T14:20:00Z',
  },
  {
    id: '3',
    name: '生活方式',
    description: '生活、美食、旅行等日常生活相关内容',
    color: 'green',
    videoCount: 18,
    createdAt: '2024-01-08T16:45:00Z',
  },
  {
    id: '4',
    name: '科技数码',
    description: '科技产品评测、技术分享、数码资讯',
    color: 'purple',
    videoCount: 30,
    createdAt: '2024-01-12T09:15:00Z',
  },
  {
    id: '5',
    name: '游戏娱乐',
    description: '游戏实况、娱乐内容、影视评论',
    color: 'orange',
    videoCount: 45,
    createdAt: '2024-01-15T20:30:00Z',
  },
  {
    id: '6',
    name: '音乐艺术',
    description: '音乐创作、艺术表演、创意设计',
    color: 'pink',
    videoCount: 8,
    createdAt: '2024-01-18T11:00:00Z',
  },
];

export default function FoldersPage() {
  const [folders, setFolders] = useState<Folder[]>(mockFolders);
  const [editingFolder, setEditingFolder] = useState<Folder | null>(null);
  const [deletingFolder, setDeletingFolder] = useState<Folder | null>(null);
  const [selectedFolder, setSelectedFolder] = useState<Folder | null>(null);
  const [isEditDrawerOpen, setIsEditDrawerOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isFolderDetailOpen, setIsFolderDetailOpen] = useState(false);

  // 处理新建文件夹
  const handleCreateFolder = () => {
    setEditingFolder(null);
    setIsEditDrawerOpen(true);
  };

  // 处理编辑文件夹
  const handleEditFolder = (folder: Folder) => {
    setEditingFolder(folder);
    setIsEditDrawerOpen(true);
  };

  // 处理删除文件夹
  const handleDeleteFolder = (folder: Folder) => {
    setDeletingFolder(folder);
    setIsDeleteDialogOpen(true);
  };

  // 处理点击文件夹
  const handleFolderClick = (folder: Folder) => {
    // 只有自定义文件夹才能点击查看详情
    if (folder.id !== '1') {
      setSelectedFolder(folder);
      setIsFolderDetailOpen(true);
    }
  };

  // 保存文件夹
  const handleSaveFolder = (
    data: Omit<Folder, 'id' | 'createdAt' | 'updatedAt'>
  ) => {
    if (editingFolder) {
      // 编辑现有文件夹
      setFolders((prev) =>
        prev.map((folder) =>
          folder.id === editingFolder.id
            ? { ...folder, ...data, updatedAt: new Date().toISOString() }
            : folder
        )
      );
      toaster.create({
        title: '更新成功',
        description: '文件夹信息已更新',
        type: 'success',
        duration: 3000,
      });
    } else {
      // 创建新文件夹
      const newFolder: Folder = {
        ...data,
        id: Date.now().toString(),
        videoCount: 0,
        createdAt: new Date().toISOString(),
      };
      setFolders((prev) => [...prev, newFolder]);
      toaster.create({
        title: '创建成功',
        description: '新文件夹已创建',
        type: 'success',
        duration: 3000,
      });
    }
    setIsEditDrawerOpen(false);
  };

  // 确认删除文件夹
  const handleConfirmDelete = () => {
    if (deletingFolder) {
      setFolders((prev) =>
        prev.filter((folder) => folder.id !== deletingFolder.id)
      );
      toaster.create({
        title: '删除成功',
        description: '文件夹已删除',
        type: 'success',
        duration: 3000,
      });
      setIsDeleteDialogOpen(false);
      setDeletingFolder(null);
    }
  };

  return (
    <Container maxW="6xl" py={8}>
      <Stack gap={8}>
        {/* 页面标题 */}
        <Flex justify="space-between" align="center">
          <Box>
            <Heading size="xl" mb={2}>
              文件夹管理
            </Heading>
            <Text color="gray.600">管理您的内容分类文件夹，组织视频和文章</Text>
          </Box>
          <Button colorScheme="blue" onClick={handleCreateFolder}>
            <AiOutlinePlus />
            新建文件夹
          </Button>
        </Flex>

        {/* 统计信息 */}
        <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={4}>
          <Card.Root variant="outline">
            <Card.Body textAlign="center" py={6}>
              <Text fontSize="2xl" fontWeight="bold" color="blue.500">
                {folders.length}
              </Text>
              <Text color="gray.600">总文件夹数</Text>
            </Card.Body>
          </Card.Root>
          <Card.Root variant="outline">
            <Card.Body textAlign="center" py={6}>
              <Text fontSize="2xl" fontWeight="bold" color="green.500">
                {folders.reduce((sum, folder) => sum + folder.videoCount, 0)}
              </Text>
              <Text color="gray.600">总视频数</Text>
            </Card.Body>
          </Card.Root>
        </Grid>

        {/* 文件夹网格 */}
        <Grid templateColumns="repeat(auto-fill, minmax(320px, 1fr))" gap={6}>
          {folders.map((folder) => (
            <Card.Root
              key={folder.id}
              variant="outline"
              className="group"
              cursor={folder.id !== '1' ? 'pointer' : 'default'}
              onClick={() => handleFolderClick(folder)}
              _hover={
                folder.id !== '1'
                  ? { shadow: 'md', borderColor: `${folder.color}.300` }
                  : {}
              }
              transition="all 0.2s"
            >
              <Card.Body>
                <Stack gap={4}>
                  {/* 文件夹头部 */}
                  <Flex justify="space-between" align="flex-start">
                    <Flex align="center" gap={3}>
                      <Box
                        p={2}
                        borderRadius="md"
                        bg={`${folder.color}.100`}
                        color={`${folder.color}.600`}
                      >
                        <AiOutlineFolderOpen size={24} />
                      </Box>
                      <Box>
                        <Heading size="md" mb={1}>
                          {folder.name}
                        </Heading>
                        <Badge
                          colorScheme={folder.color}
                          variant="subtle"
                          size="sm"
                        >
                          {folder.color === 'gray' ? '默认' : '自定义'}
                        </Badge>
                      </Box>
                    </Flex>

                    {/* 操作菜单 */}
                    <Menu.Root>
                      <Menu.Trigger asChild>
                        <IconButton
                          variant="ghost"
                          size="sm"
                          aria-label="更多操作"
                          opacity={0}
                          className="group-hover:opacity-100"
                          transition="opacity 0.2s"
                        >
                          <AiOutlineMore />
                        </IconButton>
                      </Menu.Trigger>
                      <Portal>
                        <Menu.Positioner>
                          <Menu.Content>
                            <Menu.Item
                              value="edit"
                              onClick={() => handleEditFolder(folder)}
                            >
                              <AiOutlineEdit />
                              编辑
                            </Menu.Item>
                            <Menu.Item
                              value="delete"
                              onClick={() => handleDeleteFolder(folder)}
                              disabled={folder.id === '1'} // 默认文件夹不能删除
                            >
                              <AiOutlineDelete />
                              删除
                            </Menu.Item>
                          </Menu.Content>
                        </Menu.Positioner>
                      </Portal>
                    </Menu.Root>
                  </Flex>

                  {/* 文件夹描述 */}
                  <Text fontSize="sm" color="gray.600" lineHeight="1.5">
                    {folder.description}
                  </Text>

                  {/* 内容统计 */}
                  <Flex gap={4}>
                    <Flex align="center" gap={1} fontSize="sm" color="gray.500">
                      <AiOutlineVideoCamera />
                      <Text>{folder.videoCount} 个视频</Text>
                    </Flex>
                    {folder.id !== '1' && (
                      <Text fontSize="sm" color="blue.500" fontWeight="medium">
                        点击查看详情
                      </Text>
                    )}
                  </Flex>

                  {/* 时间信息 */}
                  <Text fontSize="xs" color="gray.400">
                    创建于{' '}
                    {new Date(folder.createdAt).toLocaleDateString('zh-CN')}
                    {folder.updatedAt && (
                      <span>
                        {' '}
                        · 更新于{' '}
                        {new Date(folder.updatedAt).toLocaleDateString('zh-CN')}
                      </span>
                    )}
                  </Text>
                </Stack>
              </Card.Body>
            </Card.Root>
          ))}
        </Grid>
      </Stack>

      {/* 编辑文件夹抽屉 */}
      <FolderEditDrawer
        isOpen={isEditDrawerOpen}
        onClose={() => setIsEditDrawerOpen(false)}
        folder={editingFolder}
        onSave={handleSaveFolder}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        folder={deletingFolder}
        onConfirm={handleConfirmDelete}
      />

      {/* 文件夹详情 drawer */}
      <FolderDetailDrawer
        isOpen={isFolderDetailOpen}
        onClose={() => setIsFolderDetailOpen(false)}
        folder={selectedFolder}
        onCreateVideo={() => {
          // 这里可以添加创建视频的逻辑
          toaster.create({
            title: '功能开发中',
            description: '视频创建功能正在开发中',
            type: 'info',
            duration: 3000,
          });
        }}
      />
    </Container>
  );
}
