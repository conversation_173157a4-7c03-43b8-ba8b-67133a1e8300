'use client';
import { useState } from 'react';
import {
  Card,
  HStack,
  Avatar,
  Text,
  Stack,
  Box,
  Button,
  Grid,
  GridItem,
  Image,
} from '@chakra-ui/react';
import NextImage from 'next/image';
import dynamic from 'next/dynamic';
import {
  AiOutlineEllipsis,
  AiOutlineComment,
  AiOutlineLike,
  AiOutlineEye,
} from 'react-icons/ai';
import { IoIosShareAlt } from 'react-icons/io';
import Paint from '@/public/images/paint.svg';

const PostComment = dynamic(() => import('./post-comment'), {
  ssr: false,
});

export default function PostCard() {
  const [count, setCount] = useState(0);
  return (
    <Card.Root variant={'elevated'} w={'100%'} cursor={'pointer'}>
      <Card.Body className="flex w-full flex-col gap-2">
        <Box className="flex w-full items-center justify-between">
          <HStack gap="3">
            <Avatar.Root>
              <Avatar.Image src="https://images.unsplash.com/photo-1511806754518-53bada35f930" />
              <Avatar.Fallback name="<PERSON> Foss" />
            </Avatar.Root>
            <Stack gap="0">
              <Text fontWeight="semibold" textStyle="sm">
                Nate Foss
              </Text>
              <Text color="fg.muted" textStyle="sm">
                @natefoss
              </Text>
            </Stack>
          </HStack>
          <Button variant={'plain'}>
            <AiOutlineEllipsis />
          </Button>
        </Box>
        <Box w={'100%'} className="flex flex-col items-center gap-2">
          <PostComment
            content="<p>
    Vivamus vel enim at lorem ultricies faucibus. Cras vitae ipsum ut quam
    varius dignissim a ac tellus. Aliquam maximus mauris eget tincidunt
    interdum. Fusce vitae massa non risus congue tincidunt. Pellentesque maximus
    elit quis eros lobortis dictum.
  </p>"
          />
        </Box>
      </Card.Body>
      <Card.Footer>
        <Button variant={'plain'} color={'primary'} className="basis-1/3">
          <AiOutlineLike />
          325
        </Button>

        <Button variant={'plain'} className="basis-1/3">
          <AiOutlineComment />
          325
        </Button>
      </Card.Footer>
    </Card.Root>
  );
}
