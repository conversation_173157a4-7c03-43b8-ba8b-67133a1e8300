import { useState, useEffect } from 'react';
import { Box, Text } from '@chakra-ui/react';
import { motion } from 'framer-motion';

interface TypewriterProps {
  text: string; // 需要逐字显示的文本
  delay?: number; // 字符显示间隔时间（毫秒）
  cursorColor?: string; // 光标颜色
  loop?: boolean; // 是否循环播放
}

export const Typewriter = ({
  text,
  delay = 100,
  cursorColor = 'blue.500',
  loop = false, // 默认不循环
}: TypewriterProps) => {
  const [currentText, setCurrentText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false); // 是否处于删除状态
  const [isPaused, setIsPaused] = useState(false); // 添加暂停状态

  useEffect(() => {
    // 非循环模式且已完成显示，则停止
    if (!loop && currentIndex === text.length && !isDeleting) {
      return;
    }

    // 在完成显示或删除后添加短暂暂停
    if (isPaused) {
      const pauseTimeout = setTimeout(() => {
        setIsPaused(false);
      }, delay * 10); // 暂停时间比打字时间长
      return () => clearTimeout(pauseTimeout);
    }

    const timeout = setTimeout(() => {
      if (isDeleting) {
        // 删除字符
        setCurrentText((prevText) => prevText.slice(0, -1));

        // 当删除完成时
        if (currentText.length <= 1) {
          setIsDeleting(false);
          setIsPaused(true); // 删除完成后暂停一下
          setCurrentIndex(0); // 重置索引以重新开始
        }
      } else {
        // 添加字符
        setCurrentText((prevText) => prevText + text[currentIndex]);
        setCurrentIndex((prevIndex) => prevIndex + 1);

        // 当显示完成时
        if (currentIndex >= text.length - 1) {
          if (loop) {
            setIsPaused(true); // 显示完成后暂停一下
            setIsDeleting(true); // 然后开始删除
          }
        }
      }
    }, delay);

    return () => clearTimeout(timeout);
  }, [currentIndex, currentText, delay, text, isDeleting, loop, isPaused]);

  return (
    <Box display="inline-flex" alignItems="center">
      <Text as="span">{currentText}</Text>
      {/* 光标闪烁效果 */}
      <motion.span
        style={{
          display: 'inline-block',
          width: '2px',
          height: '1em',
          backgroundColor: cursorColor,
          marginLeft: '2px',
        }}
        animate={{ opacity: [0, 1, 0] }}
        transition={{ repeat: Infinity, duration: 0.8 }}
      />
    </Box>
  );
};
