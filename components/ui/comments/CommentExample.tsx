import React from 'react';
import { Box } from '@chakra-ui/react';
import { Comments } from './Comments';
import { useComments } from './hooks/useComments';
import { mockCurrentUser, fetchMockComments } from './mockData';

const CommentExample: React.FC = () => {
  const {
    comments,
    hasMore,
    isLoading,
    loadMore,
    addComment,
    addReply,
    likeComment,
    editComment,
    deleteComment,
  } = useComments({
    pageSize: 3,
    fetchComments: fetchMockComments,
  });

  return (
    <Box>
      <Comments
        comments={comments}
        currentUser={mockCurrentUser}
        onAddComment={(content) => addComment(content, mockCurrentUser)}
        onReply={(commentId, content) =>
          addReply(commentId, content, mockCurrentUser)
        }
        onLike={likeComment}
        onEdit={editComment}
        onDelete={deleteComment}
        onLoadMore={loadMore}
        hasMore={hasMore}
        isLoading={isLoading}
      />
    </Box>
  );
};

export default CommentExample;
