import dynamic from 'next/dynamic';
import { Container, Box, Center } from '@chakra-ui/react';
import Content from '@/app/_components/content';

const LeftSide = dynamic(() => import('./_components/left-side'));
const RightSide = dynamic(() => import('./_components/right-side'));

export default function Page() {
  /**
   * @description 视频播放页面
   */
  return (
    <Content>
      <Container
        maxW="100%"
        fluid
        className="flex h-full flex-col gap-6 overflow-y-auto scroll-smooth"
      >
        <Box h="100%">
          <Center>
            <Container maxW="6xl" fluid className="grid grid-cols-3 gap-4">
              <LeftSide />
              <RightSide />
            </Container>
          </Center>
        </Box>
      </Container>
    </Content>
  );
}
