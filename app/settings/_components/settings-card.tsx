'use client';
import {
  Card,
  AspectRatio,
  Image,
  Flex,
  Container,
  Stack,
  Box,
  HStack,
  Avatar,
  Text,
  IconButton,
  Separator,
  Popover,
  Portal,
} from '@chakra-ui/react';
import NextImage from 'next/image';
import {
  AiOutlineSmallDash,
  AiOutlineStar,
  AiOutlineLike,
} from 'react-icons/ai';

import Paint from '@/public/images/paint.svg';

export default function ArticleCard() {
  return (
    <Card.Root
      className="h-45 w-full overflow-hidden hover:shadow-lg"
      cursor="pointer"
    >
      <Card.Body p={0}>
        <Flex direction={'row'} alignItems={'center'} h={'100%'} w={'100%'}>
          <AspectRatio className="w-2/5">
            <Image asChild>
              <NextImage
                src={Paint}
                alt="article"
                className="rounded-t-md object-cover"
                layout="fill"
              />
            </Image>
          </AspectRatio>
          <Container fluid py={2}>
            <Flex direction={'column'} h={'100%'} gap={2}>
              <Card.Title truncate>文章标题</Card.Title>
              <Box className="flex-1">
                <Card.Description lineClamp={2}>文章描述</Card.Description>
              </Box>
              <HStack>
                <Avatar.Root size="xs">
                  <Avatar.Image
                    src="https://i.pravatar.cc/150?u=abcd"
                    alt="作者头像"
                  />
                  <Avatar.Fallback>AB</Avatar.Fallback>
                </Avatar.Root>

                <Text fontSize={'sm'}>作者名称</Text>
              </HStack>

              <Flex direction={'row'} align={'center'} gap={3} mb={1}>
                <Text fontSize={'sm'} color="gray.500">
                  2023-10-01
                </Text>
                <Separator orientation="vertical" height="4" />
                <Stack direction="row">
                  <IconButton size="xs" variant="ghost">
                    <AiOutlineLike />
                  </IconButton>
                  <IconButton size="xs" variant="ghost">
                    <AiOutlineStar />
                  </IconButton>
                  <Popover.Root size={'xs'}>
                    <Popover.Trigger asChild>
                      <IconButton size="xs" variant="ghost">
                        <AiOutlineSmallDash />
                      </IconButton>
                    </Popover.Trigger>
                    <Portal>
                      <Popover.Positioner>
                        <Popover.Content>
                          <Popover.Arrow />
                          <Popover.Body>
                            <Stack>
                              <IconButton
                                size="xs"
                                variant="ghost"
                                bg={'red.600'}
                                color={'white'}
                              >
                                删除
                              </IconButton>
                            </Stack>
                          </Popover.Body>
                        </Popover.Content>
                      </Popover.Positioner>
                    </Portal>
                  </Popover.Root>
                </Stack>
              </Flex>
            </Flex>
          </Container>
        </Flex>
      </Card.Body>
    </Card.Root>
  );
}
