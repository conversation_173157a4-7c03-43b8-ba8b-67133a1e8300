'use client';

import {
  Drawer,
  Portal,
  Box,
  Stack,
  Field,
  Input,
  Textarea,
  Button,
  Image,
  IconButton,
  AspectRatio,
  Flex,
  Text,
  FileUpload,
  Icon,
} from '@chakra-ui/react';
import { useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { object, string } from 'yup';
import { AiOutlineEdit, AiOutlineFolderOpen } from 'react-icons/ai';
import { LuUpload } from 'react-icons/lu';
import dynamic from 'next/dynamic';

// 动态导入 Select 组件
const Select = dynamic(
  () => import('chakra-react-select').then((mod) => mod.Select),
  { ssr: false }
);

// 视频数据类型定义
interface VideoData {
  id: string;
  title: string;
  description: string;
  coverImage: string;
  folderId: string;
}

// 文件夹选项类型
interface FolderOption {
  value: string;
  label: string;
}

// 组件 Props 类型
interface VideoEditDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  videoData?: VideoData;
  onSave: (data: VideoFormData) => void;
  onPublish: (data: VideoFormData) => void;
  folders?: FolderOption[];
}

// 表单数据类型
interface VideoFormData {
  title: string;
  description: string;
  folderId: string;
  coverImage?: File;
}

// 表单验证 schema
const schema = object({
  title: string().required('请输入视频标题'),
  description: string().required('请输入视频描述'),
  folderId: string().required('请选择文件夹'),
});

// 默认文件夹选项
const defaultFolders: FolderOption[] = [
  { value: 'default', label: '默认文件夹' },
  { value: 'entertainment', label: '娱乐' },
  { value: 'education', label: '教育' },
  { value: 'technology', label: '科技' },
  { value: 'lifestyle', label: '生活' },
];

export default function VideoEditDrawer({
  isOpen,
  onClose,
  videoData,
  onSave,
  onPublish,
  folders = defaultFolders,
}: VideoEditDrawerProps) {
  const [coverImagePreview, setCoverImagePreview] = useState<string>(
    videoData?.coverImage || ''
  );

  // 表单配置
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<VideoFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      title: videoData?.title || '',
      description: videoData?.description || '',
      folderId: videoData?.folderId || '',
    },
  });

  // 处理封面图片上传
  const handleCoverImageChange = (files: File[]) => {
    if (files.length > 0) {
      const file = files[0];
      const reader = new FileReader();
      reader.onload = (e) => {
        setCoverImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // 处理保存
  const handleSave = (data: VideoFormData) => {
    onSave(data);
  };

  // 处理发布
  const handlePublish = (data: VideoFormData) => {
    onPublish(data);
  };

  // 处理关闭
  const handleClose = () => {
    reset();
    setCoverImagePreview(videoData?.coverImage || '');
    onClose();
  };

  return (
    <Drawer.Root open={isOpen} onOpenChange={(e) => !e.open && handleClose()} size="md">
      <Portal>
        <Drawer.Backdrop />
        <Drawer.Positioner>
          <Drawer.Content>
            <Drawer.Header>
              <Drawer.Title>编辑视频</Drawer.Title>
              <Drawer.CloseTrigger />
            </Drawer.Header>

            <Drawer.Body>
              <Stack gap={6}>
                {/* 视频封面区域 */}
                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={3}>
                    视频封面
                  </Text>
                  <Flex gap={3} alignItems="flex-start">
                    <AspectRatio ratio={16 / 9} width="200px" flexShrink={0}>
                      <Box
                        borderRadius="md"
                        overflow="hidden"
                        border="1px solid"
                        borderColor="gray.200"
                        bg="gray.50"
                      >
                        {coverImagePreview ? (
                          <Image
                            src={coverImagePreview}
                            alt="视频封面"
                            objectFit="cover"
                            width="100%"
                            height="100%"
                          />
                        ) : (
                          <Flex
                            alignItems="center"
                            justifyContent="center"
                            height="100%"
                            color="gray.400"
                          >
                            <Text fontSize="sm">暂无封面</Text>
                          </Flex>
                        )}
                      </Box>
                    </AspectRatio>

                    {/* 修改封面按钮 */}
                    <FileUpload.Root
                      accept="image/*"
                      maxFiles={1}
                      onFileChange={(details) => handleCoverImageChange(details.acceptedFiles)}
                    >
                      <FileUpload.HiddenInput />
                      <FileUpload.Trigger asChild>
                        <IconButton
                          variant="outline"
                          size="sm"
                          aria-label="修改封面"
                        >
                          <AiOutlineEdit />
                        </IconButton>
                      </FileUpload.Trigger>
                    </FileUpload.Root>
                  </Flex>
                </Box>

                {/* 表单区域 */}
                <form id="video-edit-form">
                  <Stack gap={4}>
                    {/* 视频标题 */}
                    <Field.Root invalid={!!errors.title} required>
                      <Field.Label>
                        <Field.RequiredIndicator />
                        视频标题
                      </Field.Label>
                      <Input
                        placeholder="请输入视频标题"
                        {...register('title')}
                      />
                      <Field.ErrorText>{errors.title?.message}</Field.ErrorText>
                    </Field.Root>

                    {/* 视频描述 */}
                    <Field.Root invalid={!!errors.description} required>
                      <Field.Label>
                        <Field.RequiredIndicator />
                        视频描述
                      </Field.Label>
                      <Textarea
                        placeholder="请输入视频描述"
                        rows={4}
                        {...register('description')}
                      />
                      <Field.ErrorText>{errors.description?.message}</Field.ErrorText>
                    </Field.Root>

                    {/* 文件夹选择 */}
                    <Field.Root invalid={!!errors.folderId} required>
                      <Field.Label>
                        <Field.RequiredIndicator />
                        移动到文件夹
                      </Field.Label>
                      <Controller
                        name="folderId"
                        control={control}
                        render={({ field }) => (
                          <Select
                            options={folders}
                            placeholder="选择文件夹"
                            value={folders.find(folder => folder.value === field.value)}
                            onChange={(option) => field.onChange(option?.value)}
                            components={{
                              DropdownIndicator: () => (
                                <Box mr={2}>
                                  <AiOutlineFolderOpen />
                                </Box>
                              ),
                            }}
                          />
                        )}
                      />
                      <Field.ErrorText>{errors.folderId?.message}</Field.ErrorText>
                    </Field.Root>
                  </Stack>
                </form>
              </Stack>
            </Drawer.Body>

            {/* 底部按钮 */}
            <Drawer.Footer>
              <Stack direction="row" gap={3} width="100%">
                <Button
                  variant="outline"
                  onClick={handleClose}
                  flex={1}
                >
                  取消
                </Button>
                <Button
                  colorScheme="blue"
                  onClick={handleSubmit(handlePublish)}
                  isLoading={isSubmitting}
                  flex={1}
                >
                  发布
                </Button>
              </Stack>
            </Drawer.Footer>
          </Drawer.Content>
        </Drawer.Positioner>
      </Portal>
    </Drawer.Root>
  );
}
