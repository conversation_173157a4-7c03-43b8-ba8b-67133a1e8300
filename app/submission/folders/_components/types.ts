// 文件夹数据类型
export interface Folder {
  id: string;
  name: string;
  description: string;
  color: string;
  videoCount: number;
  createdAt: string;
  updatedAt?: string;
}

// 视频数据类型
export interface Video {
  id: string;
  title: string;
  description: string;
  coverImage: string;
  duration: number;
  size: number;
  folderId: string;
  status: 'draft' | 'published' | 'reviewing' | 'rejected';
  createdAt: string;
  updatedAt?: string;
}

// 表单数据类型
export interface FolderFormData {
  name: string;
  description: string;
  color: string;
}

// 视频表单数据类型
export interface VideoFormData {
  title: string;
  description: string;
  folderId: string;
}

// 颜色选项类型
export interface ColorOption {
  value: string;
  label: string;
  color: string;
}
