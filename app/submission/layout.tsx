import { Box, Container } from '@chakra-ui/react';
import SteamHeader from '../_components/header';
import Content from '../_components/content';
import LeftSide from './_components/left-side';
import dynamic from 'next/dynamic';

const Drawer = dynamic(() => import('./_components/drawer'));

export async function generateMetadata() {
  return {
    title: '极致创思聚合网站|投稿',
    content: '视频,文章,scratch编程网站聚合',
    csp: "default-src 'self'; script-src 'self' 'unsafe-inline';",
  };
}

export default function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <Box
      w="100%"
      className="bg-background text-foreground font-noto flex min-h-screen flex-col"
    >
      <SteamHeader />
      <Content>
        <Drawer />
        <Box className="grid h-full grid-cols-4 gap-3">
          <Box className="scroll-hover-hide col-span-1 h-full overflow-y-auto scroll-smooth">
            <LeftSide />
          </Box>
          <Container
            h={'100%'}
            fluid
            className="scroll-hover-hide col-span-3 overflow-y-auto scroll-smooth !py-3"
          >
            {children}
          </Container>
        </Box>
      </Content>
    </Box>
  );
}
