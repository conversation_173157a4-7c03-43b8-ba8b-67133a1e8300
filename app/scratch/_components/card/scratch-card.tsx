'use client';
import {
  Card,
  AspectRatio,
  Stack,
  Image,
  Text,
  Box,
  Avatar,
} from '@chakra-ui/react';
import NextImage from 'next/image';
import { AiFillFire } from 'react-icons/ai';

import Paint from '@/public/images/paint.svg';

export default function ScratchCard() {
  return (
    <Card.Root
      className="group h-60 rounded-md"
      cursor={'pointer'}
      overflow={'hidden'}
      variant="elevated"
    >
      <Card.Body p={0} h={'100%'}>
        <Stack gap={3} h={'100%'}>
          <AspectRatio ratio={16 / 7}>
            <Image asChild>
              <NextImage
                src={Paint}
                alt="Scratch Card"
                layout="fill"
                objectFit="cover"
                className="rounded-md"
              />
            </Image>
          </AspectRatio>
          <Box className="flex flex-col" gap={2} px={4} h={'100%'}>
            <Text
              truncate
              className="!text-md group-hover:!text-primary !font-semibold"
            >
              文章标题
            </Text>
            <Box className="flex items-center justify-between">
              <Stack direction={'row'} gap={2} className="flex items-center">
                <Avatar.Root size="xs">
                  <Avatar.Image
                    src="https://bit.ly/dan-abramov"
                    alt="username"
                  />
                </Avatar.Root>
                <Text className="!text-xs !text-gray-500">用户姓名</Text>
              </Stack>
              <Text className="flex items-center !text-xs !text-gray-500">
                <AiFillFire className="mr-1 inline-block" />
                1000万
              </Text>
            </Box>
          </Box>
        </Stack>
      </Card.Body>
    </Card.Root>
  );
}
