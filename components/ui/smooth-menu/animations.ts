import { Variants } from 'framer-motion';

// 菜单项的动画变体
export const menuItemVariants: Variants = {
  initial: {
    opacity: 0,
    y: -4,
  },
  animate: {
    opacity: 1,
    y: 0,
  },
  exit: {
    opacity: 0,
    y: -4,
  },
};

// 子菜单的动画变体
export const subMenuVariants: Variants = {
  closed: {
    opacity: 0,
    height: 0,
    transition: {
      duration: 0.2,
      ease: 'easeInOut',
    },
  },
  open: {
    opacity: 1,
    height: 'auto',
    transition: {
      duration: 0.2,
      ease: 'easeInOut',
    },
  },
};

// 子菜单内容的动画变体
export const subMenuContentVariants: Variants = {
  closed: {
    opacity: 0,
    x: -10,
  },
  open: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.2,
      ease: 'easeOut',
      staggerChildren: 0.05,
    },
  },
};

// 菜单项hover动画配置
export const menuItemHoverAnimation = {
  whileHover: {
    backgroundColor: 'rgba(0, 0, 0, 0.04)',
    transition: { duration: 0.2 },
  },
  whileTap: {
    scale: 0.98,
  },
};

// 子菜单箭头动画配置
export const arrowVariants: Variants = {
  closed: {
    rotate: 0,
    transition: { duration: 0.2 },
  },
  open: {
    rotate: 90,
    transition: { duration: 0.2 },
  },
};
