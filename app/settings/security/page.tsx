import { Container, Flex } from '@chakra-ui/react';
import SecurityPassword from './_components/security-password';
import SecurityDelete from './_components/security-delete';

export default function Security() {
  /**
   * @description 安全设置页面
   */
  return (
    <Container maxW="6xl" h={'100%'} py={6}>
      <Flex direction={'column'} gap={6}>
        <SecurityPassword />
        <SecurityDelete />
      </Flex>
    </Container>
  );
}
