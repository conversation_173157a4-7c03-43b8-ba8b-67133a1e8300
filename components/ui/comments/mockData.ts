import { Comment } from './types';

// 模拟用户数据
export const mockUsers = {
  user1: {
    id: 'user1',
    name: '张三',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
  },
  user2: {
    id: 'user2',
    name: '李四',
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
  },
  user3: {
    id: 'user3',
    name: '王五',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
  },
  user4: {
    id: 'user4',
    name: '赵六',
    avatar: 'https://randomuser.me/api/portraits/women/4.jpg',
  },
  user5: {
    id: 'user5',
    name: '钱七',
    avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
  },
};

// 模拟评论数据
export const mockComments: Comment[] = [
  {
    id: 'comment1',
    content: '这篇文章写得非常好，我学到了很多东西！期待更多类似的内容。',
    author: mockUsers.user1,
    createdAt: '2023-05-15T08:30:00Z',
    likes: 24,
    replies: [
      {
        id: 'reply1-1',
        content: '我也觉得很棒，特别是关于React Hooks的部分讲解得很清晰。',
        author: mockUsers.user2,
        createdAt: '2023-05-15T09:15:00Z',
        likes: 8,
        parentId: 'comment1',
      },
      {
        id: 'reply1-2',
        content: '同意！作者的解释方式很容易理解。',
        author: mockUsers.user3,
        createdAt: '2023-05-15T10:20:00Z',
        likes: 5,
        parentId: 'comment1',
      },
    ],
  },
  {
    id: 'comment2',
    content:
      '我对文章中提到的性能优化技巧有些疑问，特别是关于React.memo的使用场景，能否详细解释一下？',
    author: mockUsers.user4,
    createdAt: '2023-05-16T14:45:00Z',
    likes: 12,
    replies: [
      {
        id: 'reply2-1',
        content:
          '不是作者，但我可以分享一下我的理解。React.memo主要用于避免组件在props没有变化时的重新渲染，适用于纯展示型组件或计算量大的组件。',
        author: mockUsers.user5,
        createdAt: '2023-05-16T15:30:00Z',
        likes: 15,
        parentId: 'comment2',
      },
      {
        id: 'reply2-2',
        content:
          '补充一点，使用React.memo时需要注意，如果组件内部使用了context或state，即使props没变，组件也会重新渲染。',
        author: mockUsers.user1,
        createdAt: '2023-05-16T16:10:00Z',
        likes: 7,
        parentId: 'comment2',
      },
    ],
  },
  {
    id: 'comment3',
    content: '文章中的代码示例非常实用，我已经在我的项目中应用了，效果很好！',
    author: mockUsers.user3,
    createdAt: '2023-05-17T11:20:00Z',
    likes: 18,
    replies: [],
  },
  {
    id: 'comment4',
    content:
      '我发现文章中有一个小错误，在讲解useEffect清理函数的部分，示例代码中return的函数写法有问题。',
    author: mockUsers.user2,
    createdAt: '2023-05-18T09:05:00Z',
    likes: 6,
    replies: [
      {
        id: 'reply4-1',
        content: '你说得对，应该是return一个函数，而不是直接执行清理逻辑。',
        author: mockUsers.user5,
        createdAt: '2023-05-18T09:30:00Z',
        likes: 3,
        parentId: 'comment4',
      },
    ],
  },
  {
    id: 'comment5',
    content:
      '希望能有更多关于React Server Components的内容，这是React的未来发展方向。',
    author: mockUsers.user5,
    createdAt: '2023-05-19T16:40:00Z',
    likes: 9,
    replies: [],
  },
];

// 模拟当前用户
export const mockCurrentUser = mockUsers.user1;

// 模拟获取评论的函数
export const fetchMockComments = async (
  page: number,
  pageSize: number
): Promise<Comment[]> => {
  // 模拟API延迟
  await new Promise((resolve) => setTimeout(resolve, 800));

  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  return mockComments.slice(startIndex, endIndex);
};

// 使用示例
export const CommentListExample = `
import React from 'react';
import { Box } from '@chakra-ui/react';
import { Comments, useComments } from '@/components/ui/comments';
import { mockCurrentUser, fetchMockComments } from '@/components/ui/comments/mockData';

const CommentExample = () => {
  const {
    comments,
    hasMore,
    isLoading,
    loadMore,
    addComment,
    addReply,
    likeComment,
    editComment,
    deleteComment,
  } = useComments({
    pageSize: 3,
    fetchComments: fetchMockComments,
  });

  return (
    <Box maxW="800px" mx="auto" p={4}>
      <Comments
        comments={comments}
        currentUser={mockCurrentUser}
        onAddComment={(content) => addComment(content, mockCurrentUser)}
        onReply={(commentId, content) => addReply(commentId, content, mockCurrentUser)}
        onLike={likeComment}
        onEdit={editComment}
        onDelete={deleteComment}
        onLoadMore={loadMore}
        hasMore={hasMore}
        isLoading={isLoading}
      />
    </Box>
  );
};

export default CommentExample;
`;
