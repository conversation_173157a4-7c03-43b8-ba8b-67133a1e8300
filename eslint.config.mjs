import { dirname } from 'path';
import { fileURLToPath } from 'url';
import { FlatCompat } from '@eslint/eslintrc';

import reactYouMightNotNeedAnEffect from 'eslint-plugin-react-you-might-not-need-an-effect';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends('next/core-web-vitals', 'next/typescript', 'prettier'),
  {
    plugins: {
      '@next/next': next,
      'react-you-might-not-need-an-effect': reactYouMightNotNeedAnEffect,
      import: require('eslint-plugin-import'),
    },
    rules: {
      // 性能相关
      '@next/next/no-img-element': 'warn',
      '@next/next/no-sync-scripts': 'error',
      '@next/next/no-unwanted-polyfillio': 'error',

      // 最佳实践
      '@next/next/no-typos': 'error',
      '@next/next/no-head-element': 'error',
      '@next/next/no-html-link-for-pages': 'error',

      // 字体优化
      '@next/next/google-font-display': 'warn',
      '@next/next/google-font-preconnect': 'warn',
      'import/order': [
        'error',
        {
          groups: [
            'builtin', // Node.js 内置模块
            'external', // 第三方依赖
            'internal', // 项目内部别名路径
            'parent', // 父级目录导入
            'sibling', // 同级目录导入
            'index', // 当前目录的 index 文件
            'object', // 对象导入 (如 CSS)
            'type', // TypeScript 类型
          ],
          'newlines-between': 'always', // 组间用空行分隔
          alphabetize: {
            order: 'asc', // 字母顺序排序
            caseInsensitive: true, // 不区分大小写
          },
          pathGroups: [
            {
              pattern: '@/**', // 将 @/ 开头的路径视为内部模块
              group: 'internal',
            },
            {
              pattern: '*.{css,scss}', // 将 CSS 文件单独分组
              group: 'object',
              patternOptions: { matchBase: true },
            },
          ],
        },
      ],
    },
  },
];

export default eslintConfig;
