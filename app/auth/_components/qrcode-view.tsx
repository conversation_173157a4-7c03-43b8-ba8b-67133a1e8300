import { Box, Center, QrCode, AbsoluteCenter, Spinner } from '@chakra-ui/react';

export default function QRCode() {
  /**
   * @description QRCode component
   * @returns {JSX.Element}
   */
  return (
    <Box className="w-full p-4">
      <Center h-full>
        <Box position="relative">
          <QrCode.Root size={'xl'} value="https://www.google.com">
            <QrCode.Frame>
              <QrCode.Pattern />
            </QrCode.Frame>
            <AbsoluteCenter bg="bg/80" boxSize="100%">
              <Spinner color="red" />
            </AbsoluteCenter>
          </QrCode.Root>
        </Box>
      </Center>
    </Box>
  );
}
