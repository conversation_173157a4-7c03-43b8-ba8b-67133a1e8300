'use client';
import {
  VStack,
  Text,
  Avatar,
  Icon,
  HStack,
  Card,
  Box,
  IconButton,
} from '@chakra-ui/react';
import { AiOutlineLike, AiOutlineShareAlt } from 'react-icons/ai';
import dynamic from 'next/dynamic';

const PostCommentButton = dynamic(() => import('./post-comment'));

// 模拟的帖子数据
const mockPosts = [
  {
    id: 1,
    content:
      '本文介绍了聚合物蒸汽技术的最新研究成果，包括新型催化剂的应用和工艺优化方案...',
    author: {
      name: '张工程师',
      avatar: 'https://bit.ly/dan-abramov',
    },
    likes: 42,
    comments: 8,
    shares: 3,
    time: '2小时前',
  },
  {
    id: 2,
    content:
      '通过优化反应条件、选择合适的催化剂、控制温度参数等方法，可以显著提高聚合效率...',
    author: {
      name: '李研究员',
      avatar: 'https://bit.ly/ryan-florence',
    },
    likes: 38,
    comments: 12,
    shares: 5,
    time: '5小时前',
  },
  {
    id: 3,
    content:
      '随着新能源技术的发展，聚合物材料在太阳能电池、燃料电池等领域展现出广阔的应用前景...',
    author: {
      name: '王博士',
      avatar: 'https://bit.ly/prosper-baba',
    },
    likes: 56,
    comments: 15,
    shares: 9,
    time: '昨天',
  },
];

/**
 * @description 渲染帖子列表的组件，包含帖子卡片、作者信息、内容和互动按钮
 * @returns 由多个帖子卡片组成的垂直堆叠列表
 */
export default function PostsList() {
  /**
   * @description 帖子列表
   */
  return (
    <VStack gap="6" align="stretch">
      {mockPosts.map((post) => (
        <Card.Root
          key={post.id}
          borderWidth="1px"
          borderColor="border.default" // 使用语义化颜色令牌
          borderRadius="md"
          overflow="hidden"
          bg="bg.card" // 使用语义化颜色令牌
          boxShadow="sm"
          transition="all 0.2s"
          _hover={{ boxShadow: 'md' }}
        >
          <Card.Header pb="2">
            <HStack gap="4">
              <Avatar.Root size="sm">
                <Avatar.Image src={post.author.avatar} alt={post.author.name} />
                <Avatar.Fallback>{post.author.name.charAt(0)}</Avatar.Fallback>
              </Avatar.Root>
              <Box>
                <Text fontWeight="bold">{post.author.name}</Text>
                <Text fontSize="sm" color="text.secondary">
                  {post.time}
                </Text>
              </Box>
            </HStack>
          </Card.Header>
          <Card.Body py="2">
            <Text color="fg.muted" _dark={{ color: 'white' }}>
              {post.content}
            </Text>
            {/* 点赞、评论、分享按钮 */}
            <HStack gap="6">
              <HStack gap="1">
                <IconButton variant="ghost" px={2}>
                  <Icon as={AiOutlineLike} boxSize="18px" />
                  <Text fontSize="sm">{post.likes}</Text>
                </IconButton>
              </HStack>
              <HStack gap="1">
                {/* 评论组件 */}
                <PostCommentButton id={post.id} comments={post.comments} />
              </HStack>
              <HStack gap="1">
                <IconButton variant="ghost" px={2}>
                  <Icon as={AiOutlineShareAlt} boxSize="18px" />
                  <Text fontSize="sm">{post.shares}</Text>
                </IconButton>
              </HStack>
            </HStack>
            <div id={`comment-preview-${post.id}`}></div>
          </Card.Body>
        </Card.Root>
      ))}
    </VStack>
  );
}
