'use client';
import React, { useState } from 'react';
import {
  Flex,
  Box,
  Heading,
  Text,
  HStack,
  Stack,
  Avatar,
  IconButton,
  Badge,
} from '@chakra-ui/react';
import { useRouter } from 'next/navigation';
import { BsA<PERSON>, <PERSON>sHeartFill } from 'react-icons/bs';
import { FaEdit } from 'react-icons/fa';
import { motion } from 'framer-motion';

// 创建 motion 版本的组件
const MotionFlex = motion(Flex);
const MotionIconButton = motion(IconButton);
const MotionBox = motion(Box);
const MotionHeading = motion(Heading);
const MotionHStack = motion(HStack);

export default function RightSide() {
  const [articles, setArticles] = useState(5);
  const router = useRouter();

  const handleNavigate = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    router.push('/articles/edit');
  };

  return (
    <MotionFlex
      direction="column"
      h="100%"
      position="relative"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      gap={6}
    >
      <MotionBox
        borderWidth="2px"
        borderColor="border.disabled"
        p="2"
        className="rounded-md"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <Flex direction="column" gap="2" className="rounded-md">
          <MotionBox
            className="flex justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <MotionHeading
              size="xl"
              className="flex items-center gap-2 tracking-wide"
              whileHover={{ scale: 1.03 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                initial={{ rotate: -10 }}
                animate={{ rotate: 0 }}
                transition={{ duration: 0.5 }}
              >
                <BsAward />
              </motion.div>
              推荐文章
            </MotionHeading>
          </MotionBox>

          {Array.from({ length: articles }).map((_, index) => (
            <MotionHStack
              mb="2"
              gap="2"
              key={index}
              className="cursor-pointer"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{
                duration: 0.3,
                delay: 0.2 + 0.1 * index,
              }}
              whileHover={{
                scale: 1.02,
                backgroundColor: 'rgba(0, 0, 0, 0.05)',
              }}
            >
              <Avatar.Root shape="rounded" className="group">
                <Avatar.Image src="https://images.unsplash.com/photo-1511806754518-53bada35f930" />
                <Avatar.Fallback name="Nate Foss" />
              </Avatar.Root>
              <Stack gap={0}>
                <Text
                  fontWeight="semibold"
                  textStyle="sm"
                  className="group-hover:!text-primary"
                >
                  文章名称-{index}
                </Text>
                <Badge color={'gray.500'} variant="plain" px="0">
                  <BsHeartFill />
                  3.9k
                </Badge>
              </Stack>
            </MotionHStack>
          ))}
        </Flex>
      </MotionBox>

      <MotionIconButton
        bg={'primary'}
        color={'white'}
        variant="outline"
        onClick={handleNavigate}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.5 }}
        whileHover={{
          scale: 1.05,
          boxShadow: '0px 5px 15px rgba(0, 0, 0, 0.1)',
        }}
        whileTap={{ scale: 0.95 }}
      >
        <motion.div
          initial={{ rotate: 0 }}
          animate={{ rotate: [0, -10, 0] }}
          transition={{ duration: 1, repeat: Infinity, repeatDelay: 3 }}
        >
          <FaEdit />
        </motion.div>
        创建文章
      </MotionIconButton>
    </MotionFlex>
  );
}
