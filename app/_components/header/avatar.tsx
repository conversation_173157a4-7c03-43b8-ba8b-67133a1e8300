'use client';
import { memo } from 'react';
import { Avatar, defineStyle } from '@chakra-ui/react';

const ringCss = defineStyle({
  outlineWidth: '2px',
  outlineColor: 'colorPalette.500',
  outlineOffset: '2px',
  outlineStyle: 'solid',
});

interface IProps {
  handleMenuState: () => void;
}

const UserAvatar = ({ handleMenuState }: IProps) => {
  /**
   * @description 用户头像
   */
  return (
    <Avatar.Root
      css={ringCss}
      size="xs"
      className="cursor-pointer"
      colorPalette="pink"
      onClick={handleMenuState}
    >
      <Avatar.Fallback name="username" />
      <Avatar.Image src="https://bit.ly/sage-adebayo" />
    </Avatar.Root>
  );
};

export default memo(UserAvatar);
