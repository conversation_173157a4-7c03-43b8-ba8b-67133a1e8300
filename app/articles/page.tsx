'use client';
import dynamic from 'next/dynamic';
import React, { useState, useEffect } from 'react';
import { Container, Box } from '@chakra-ui/react';
import Content from '../_components/content';
import {
  Panel,
  PanelGroup,
  ImperativePanelHandle,
} from 'react-resizable-panels';
import { useMediaQuery } from '@chakra-ui/react';
import { useLocalStorage } from 'react-use';
import { ResizeHandle } from './_components/resize-handle';
import { motion, AnimatePresence } from 'framer-motion';

// 使用动态导入并添加加载状态
const LeftSide = dynamic(() => import('./_components/left-side'), {
  loading: () => <LoadingPlaceholder />,
});
const RightSide = dynamic(() => import('./_components/right-side'), {
  loading: () => <LoadingPlaceholder />,
});
const ArticleTabs = dynamic(() => import('./_components/article-content'), {
  loading: () => <LoadingPlaceholder />,
});

// 加载占位组件
function LoadingPlaceholder() {
  return (
    <Box
      w="100%"
      h="100%"
      display="flex"
      alignItems="center"
      justifyContent="center"
    >
      <motion.div
        initial={{ opacity: 0.5, scale: 0.9 }}
        animate={{
          opacity: [0.5, 0.8, 0.5],
          scale: [0.9, 1, 0.9],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        style={{
          width: '80%',
          height: '80%',
          background:
            'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
          backgroundSize: '200% 100%',
          borderRadius: '8px',
        }}
      />
    </Box>
  );
}

// 创建 motion 版本的组件
const MotionContainer = motion(Container);

export default function Page() {
  // 用于控制面板折叠/展开的引用
  const leftPanelRef = React.useRef<ImperativePanelHandle>(null);
  const rightPanelRef = React.useRef<ImperativePanelHandle>(null);

  // 保存面板布局
  const [savedLayout, setSavedLayout] = useLocalStorage(
    'article-page-layout',
    [20, 60, 20]
  );

  // 响应式布局：在小屏幕上自动折叠侧边栏
  const [isMobile] = useMediaQuery(['(max-width: 768px)']);

  // 页面加载状态
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // 模拟页面加载完成
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  React.useEffect(() => {
    if (isMobile) {
      leftPanelRef.current?.collapse();
      rightPanelRef.current?.collapse();
    } else {
      leftPanelRef.current?.expand();
      rightPanelRef.current?.expand();
    }
  }, [isMobile]);

  const handleLayoutChange = (sizes: number[]) => {
    setSavedLayout(sizes);
  };

  return (
    <Content>
      <AnimatePresence>
        <MotionContainer
          maxW="100%"
          fluid
          px={0}
          h="calc(100vh - 60px)"
          overflow="hidden"
          initial={{ opacity: 0 }}
          animate={{
            opacity: 1,
            transition: { duration: 0.5 },
          }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="h-full w-full"
            initial={{ opacity: 0, y: 20 }}
            animate={{
              opacity: 1,
              y: 0,
              transition: {
                duration: 0.5,
                delay: 0.2,
              },
            }}
          >
            <PanelGroup
              direction="horizontal"
              onLayout={handleLayoutChange}
              className="h-full"
            >
              {/* 左侧导航栏 */}
              <Panel
                ref={leftPanelRef}
                defaultSize={20}
                minSize={15}
                maxSize={30}
                collapsible
                collapsedSize={0}
                style={{
                  transition: 'all 0.3s ease',
                }}
              >
                <LeftSide />
              </Panel>

              {/* 左侧调整手柄 */}
              <ResizeHandle />

              {/* 中间文章列表 */}
              <Panel
                defaultSize={60}
                minSize={40}
                style={{
                  transition: 'all 0.3s ease',
                }}
              >
                <motion.div
                  className="h-full w-full"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: 1,
                    transition: {
                      duration: 0.5,
                      delay: 0.3,
                    },
                  }}
                >
                  <ArticleTabs />
                </motion.div>
              </Panel>

              {/* 右侧调整手柄 */}
              <ResizeHandle />

              {/* 右侧推荐区域 */}
              <Panel
                ref={rightPanelRef}
                defaultSize={20}
                minSize={15}
                maxSize={30}
                collapsible
                collapsedSize={0}
                style={{
                  transition: 'all 0.3s ease',
                }}
              >
                <RightSide />
              </Panel>
            </PanelGroup>
          </motion.div>
        </MotionContainer>
      </AnimatePresence>
    </Content>
  );
}
