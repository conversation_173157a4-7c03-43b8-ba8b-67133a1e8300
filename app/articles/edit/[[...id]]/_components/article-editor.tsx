'use client';
import { useState } from 'react';
import { Editor } from '@bytemd/react';
import { useMount, useUnmount } from 'ahooks';
import gfm from '@bytemd/plugin-gfm';
import highlight from '@bytemd/plugin-highlight';
import zhHans from 'bytemd/lib/locales/zh_Hans.json';
import 'github-markdown-css/github-markdown-light.css';
import 'bytemd/dist/index.css';
import 'highlight.js/styles/vs.css';

import { useArticleCreateStore } from '@/store/article-create-store';

const plugins = [gfm(), highlight()];

const ArticleMdEditor = () => {
  /**
   * @description 文章编辑器
   */
  const [value, setValue] = useState('');
  const handleShow = useArticleCreateStore((state) => state.handleShow);
  useMount(() => {
    /**
     * @description 显示文章编辑器
     */
    handleShow();
  });
  useUnmount(() => {
    /**
     * @description 隐藏文章编辑器
     */
    handleShow();
  });
  const onChange = (v: string) => {
    setValue(v);
  };
  return (
    <div className="md-editor">
      <Editor
        value={value || ''}
        locale={zhHans}
        placeholder={'请输入内容'}
        mode="split"
        plugins={plugins}
        onChange={onChange}
      />
    </div>
  );
};

export default ArticleMdEditor;
