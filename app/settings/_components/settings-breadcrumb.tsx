import { Heading, Stack, Breadcrumb } from '@chakra-ui/react';

interface IProps {
  title: string;
}

export default function SettingsBreadcrumb({ title }: IProps) {
  return (
    <Stack gap={2}>
      <Heading size={'md'}>{title}</Heading>
      <Breadcrumb.Root>
        <Breadcrumb.List>
          <Breadcrumb.Item>
            <Breadcrumb.Link href="#">设置</Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.Link href="#">文章</Breadcrumb.Link>
          </Breadcrumb.Item>
          <Breadcrumb.Separator />
          <Breadcrumb.Item>
            <Breadcrumb.CurrentLink>我的收藏</Breadcrumb.CurrentLink>
          </Breadcrumb.Item>
        </Breadcrumb.List>
      </Breadcrumb.Root>
    </Stack>
  );
}
