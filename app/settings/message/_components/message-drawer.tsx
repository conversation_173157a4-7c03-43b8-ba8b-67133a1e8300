'use client';

import { Dialog, Portal } from '@chakra-ui/react';
import { useMessageDrawerStore } from '@store/message-drawer-store';

/**
 * 消息抽屉组件，基于 Dialog 实现的可控制显示/隐藏的抽屉式弹窗
 *
 * @param children - 抽屉内容区域显示的 React 子节点
 * @returns 返回一个包含标题和内容区域的抽屉式弹窗组件
 */
export default function MessageDrawer({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const isOpen = useMessageDrawerStore((state) => state.isOpen);
  const handleDrawerState = useMessageDrawerStore(
    (state) => state.handleDrawerState
  );

  return (
    <Dialog.Root
      lazyMount
      open={isOpen}
      onOpenChange={(e) => handleDrawerState(e.open)}
    >
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content>
            <Dialog.Header>
              <Dialog.Title>消息详情</Dialog.Title>
              <Dialog.CloseTrigger />
            </Dialog.Header>
            <Dialog.Body>{children}</Dialog.Body>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
