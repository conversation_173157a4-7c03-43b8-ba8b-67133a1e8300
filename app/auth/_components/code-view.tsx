'use client';
import { useState } from 'react';
import {
  Field,
  Fieldset,
  Input,
  defineStyle,
  Box,
  Stack,
  Button,
  Checkbox,
  Link,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

const floatingStyles = defineStyle({
  pos: 'absolute',
  bg: 'bg',
  px: '0.5',
  top: '-3',
  insetStart: '2',
  fontWeight: 'normal',
  pointerEvents: 'none',
  transition: 'position',
  _peerPlaceholderShown: {
    color: 'fg.muted',
    top: '2.5',
    insetStart: '3',
  },
  _peerFocusVisible: {
    color: 'fg',
    top: '-3',
    insetStart: '2',
  },
});

const schema = yup.object({
  phone: yup
    .string()
    .required('请输入手机号')
    .matches(/^1[3-9]\d{9}$/, '手机号格式不正确'),
  code: yup.string().required('请输入验证码'),
});

export default function CodeView() {
  const [checked, setChecked] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm({
    resolver: yupResolver(schema),
    mode: 'onTouched',
  });

  const onSubmit = (data: any) => {
    // 处理登录逻辑
    console.log(data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Fieldset.Root size={'lg'}>
        <Fieldset.Content>
          <Field.Root>
            <Box pos="relative" w="full">
              <Input
                variant={'outline'}
                placeholder=""
                {...register('phone')}
                className="peer"
              />
              <Field.Label css={floatingStyles}>手机号</Field.Label>
            </Box>
            {errors.phone && (
              <Box color="red.500" fontSize="sm" mt={1}>
                {errors.phone.message as string}
              </Box>
            )}
          </Field.Root>
          <Stack direction={'row'} gap={4}>
            <Field.Root>
              <Box pos="relative" w="full">
                <Input
                  variant={'outline'}
                  placeholder=""
                  {...register('code')}
                  className="peer"
                />
                <Field.Label css={floatingStyles}>验证码</Field.Label>
              </Box>
              {errors.code && (
                <Box color="red.500" fontSize="sm" mt={1}>
                  {errors.code.message as string}
                </Box>
              )}
            </Field.Root>
            <Button
              color={'primary'}
              variant={'plain'}
              size={'lg'}
              type="button"
            >
              获取验证码
            </Button>
          </Stack>
        </Fieldset.Content>
        <Checkbox.Root checked={checked} onChange={() => setChecked(!checked)}>
          <Checkbox.HiddenInput />
          <Checkbox.Control />
          <Checkbox.Label>
            已阅读并同意
            <Link color="primary" variant="plain" href="#">
              《用户协议与隐私政策》
            </Link>
          </Checkbox.Label>
        </Checkbox.Root>
        <Box mt={4} w="100%">
          <Button w={'100%'} type="submit" disabled={!isValid || !checked}>
            登录
          </Button>
        </Box>
      </Fieldset.Root>
    </form>
  );
}
