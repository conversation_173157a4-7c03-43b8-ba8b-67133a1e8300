'use client';
import { Suspense, useState, useEffect } from 'react';
import { AspectRatio } from '@chakra-ui/react';
import dynamic from 'next/dynamic';
// import ReactPlayer from 'react-player';
// 动态导入 react-player，禁用 SSR
const ReactPlayer = dynamic(() => import('react-player/lazy'), { ssr: false });

export default function VideoPlayer() {
  const [hasWindow, setHasWindow] = useState(false);

  const url =
    'https://shaomingyang.oss-cn-beijing.aliyuncs.com/29853811895-1-192.mp4';
  return (
    <AspectRatio ratio={16 / 9} bg="bg.muted">
      <Suspense fallback={<div>Loading...</div>}>
        <ReactPlayer
          url="https://www.youtube.com/watch?v=zohpogt56Bg"
          width={'100%'}
          height={'auto'}
          controls
        />
      </Suspense>
    </AspectRatio>
  );
}
