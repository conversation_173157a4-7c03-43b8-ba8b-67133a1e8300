'use client';
import {
  Box,
  Heading,
  Container,
  Text,
  Button,
  SimpleGrid,
  Card,
  CardBody,
  Stack,
  Image,
  VStack,
  HStack,
} from '@chakra-ui/react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import SteamHeader from './_components/header';
import Content from './_components/content';

// 创建 motion 版本的组件
const MotionBox = motion(Box);
const MotionCard = motion(Card.Root);

export default function Home() {
  /**
   * @description steam 聚合网站首页
   */
  return (
    <Box
      w="100%"
      className="bg-background text-foreground font-noto flex min-h-screen flex-col"
    >
      <SteamHeader />
      <Content>
        <Container
          maxW="container.xl"
          py={8}
          h="100%"
          className="overflow-y-auto"
        >
          <VStack gap={10} align="stretch">
            {/* 欢迎区域 */}
            <MotionBox
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              textAlign="center"
              py={10}
            >
              <Heading as="h1" size="2xl" mb={4}>
                欢迎来到极致创思聚合平台
              </Heading>
              <Text
                fontSize="xl"
                color="gray.500"
                maxW="container.md"
                mx="auto"
                mb={8}
              >
                探索丰富的文章、视频和创意内容，激发您的灵感和创造力
              </Text>
              <HStack gap={4} justify="center">
                <Link href="/articles">浏览文章</Link>
                <Link href="/animation-demo">动画演示</Link>
              </HStack>
            </MotionBox>

            {/* 特色内容区域 */}
            <Box>
              <Heading as="h2" size="lg" mb={6} textAlign="center">
                探索特色内容
              </Heading>
              <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} gap={6}>
                {[
                  {
                    title: '精选文章',
                    description: '发现最新的思想和创意，深入了解各种主题',
                    image:
                      'https://images.unsplash.com/photo-1499750310107-5fef28a66643',
                    link: '/articles',
                  },
                  {
                    title: '视频教程',
                    description: '观看高质量的教程和演示，学习新技能',
                    image:
                      'https://images.unsplash.com/photo-1536240478700-b869070f9279',
                    link: '/',
                  },
                  {
                    title: 'Scratch 编程',
                    description: '探索互动编程项目，激发创造力和逻辑思维',
                    image:
                      'https://images.unsplash.com/photo-1517180102446-f3ece451e9d8',
                    link: '/',
                  },
                ].map((item, index) => (
                  <MotionCard
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    overflow="hidden"
                    variant="outline"
                    _hover={{ transform: 'translateY(-5px)', shadow: 'md' }}
                  >
                    <Image
                      src={item.image}
                      alt={item.title}
                      height="200px"
                      objectFit="cover"
                    />
                    <CardBody>
                      <Stack gap={3}>
                        <Heading size="md">{item.title}</Heading>
                        <Text>{item.description}</Text>
                        <Link href={item.link}>了解更多 &rarr;</Link>
                      </Stack>
                    </CardBody>
                  </MotionCard>
                ))}
              </SimpleGrid>
            </Box>

            {/* 动画演示链接 */}
            <MotionBox
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              bg="purple.50"
              _dark={{ bg: 'purple.900' }}
              p={6}
              borderRadius="lg"
              textAlign="center"
            >
              <Heading as="h3" size="md" mb={3}>
                体验流畅的页面动画效果
              </Heading>
              <Text mb={4}>
                我们为整个网站添加了精美的动画效果，提升用户体验
              </Text>
              <Link href="/animation-demo">
                查看动画演示<Box as="span">→</Box>
              </Link>
            </MotionBox>
          </VStack>
        </Container>
      </Content>
    </Box>
  );
}
