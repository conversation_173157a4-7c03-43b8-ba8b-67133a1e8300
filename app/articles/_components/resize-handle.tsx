'use client';
import React, { useState } from 'react';
import { PanelResizeHandle } from 'react-resizable-panels';
import { Box } from '@chakra-ui/react';
import { motion } from 'framer-motion';

interface ResizeHandleProps {
  className?: string;
  id?: string;
}

const MotionBox = motion(Box);

export function ResizeHandle({ className = '', id }: ResizeHandleProps) {
  const [isDragging, setIsDragging] = useState(false);

  return (
    <PanelResizeHandle
      id={id}
      className={`group relative flex w-2 items-center justify-center bg-transparent transition-colors ${className}`}
      onDragging={(dragging) => setIsDragging(dragging)}
    >
      <MotionBox
        className="absolute h-full rounded-sm opacity-0 transition-opacity group-hover:opacity-100 group-active:opacity-100"
        initial={{ width: '4px' }}
        animate={{
          width: isDragging ? '6px' : '4px',
          backgroundColor: isDragging
            ? 'var(--chakra-colors-blue-500)'
            : 'var(--chakra-colors-gray-200)',
        }}
        whileHover={{
          width: '6px',
          backgroundColor: 'var(--chakra-colors-blue-400)',
        }}
        transition={{
          duration: 0.2,
          ease: 'easeInOut',
        }}
        style={{
          left: '50%',
          transform: 'translateX(-50%)',
        }}
      >
        <motion.div
          className="h-full w-full"
          animate={{
            scale: isDragging ? [1, 1.2, 1] : 1,
          }}
          transition={{
            duration: 0.5,
            repeat: isDragging ? Infinity : 0,
            ease: 'easeInOut',
          }}
        />
      </MotionBox>
    </PanelResizeHandle>
  );
}
