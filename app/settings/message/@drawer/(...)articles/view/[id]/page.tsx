'use client';
import { useEffect } from 'react';
import MessageDrawer from '../../../../_components/message-drawer';
import { useMessageDrawerStore } from '@store/message-drawer-store';

export default function Page() {
  const handleDrawerState = useMessageDrawerStore(
    (state) => state.handleDrawerState
  );

  useEffect(() => {
    handleDrawerState(true);
  }, []);
  return (
    <MessageDrawer>
      <h1>文章消息抽屉内容</h1>
      <p>这里是消息抽屉的内容区域。</p>
    </MessageDrawer>
  );
}
