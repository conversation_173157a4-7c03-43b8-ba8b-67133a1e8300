import { Geist, <PERSON>eist_Mono } from 'next/font/google';
import localFont from 'next/font/local';
import { Provider } from '@/components/ui/provider';
import ThemeProvider from '@provider/theme-provider';
import dynamic from 'next/dynamic';
import './globals.css';

// 侧边消息通知导航栏
const NotificationDrawer = dynamic(
  () => import('./_components/notification-drawer')
);

// 页面切换动画组件
const PageTransition = dynamic(() => import('./_components/page-transition'));

export async function generateMetadata() {
  return {
    title: '极致创思聚合网站',
    content: '视频,文章,scratch编程网站聚合',
    csp: "default-src 'self'; script-src 'self' 'unsafe-inline';",
  };
}

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

const notoSansSC = localFont({
  src: [
    {
      path: '../public/fonts/NotoSansSC-Subset-Regular.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../public/fonts/NotoSansSC-Subset-Medium.woff2',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../public/fonts/NotoSansSC-Subset-Bold.woff2',
      weight: '700',
      style: 'normal',
    },
  ],
  variable: '--font-noto-sans-sc',
});

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${notoSansSC.variable} font-sans antialiased`}
      >
        <Provider>
          <ThemeProvider>
            <NotificationDrawer />
            <PageTransition>{children}</PageTransition>
          </ThemeProvider>
        </Provider>
      </body>
    </html>
  );
}
