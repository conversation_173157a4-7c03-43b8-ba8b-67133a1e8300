import { Box, HStack, Text } from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { MenuItemProps } from './types';
import { menuItemHoverAnimation, menuItemVariants } from './animations';

const MotionBox = motion(Box);

export const MenuItem = ({ item, isSelected, onSelect }: MenuItemProps) => {
  const handleClick = () => {
    if (!item.disabled && item.onClick) {
      item.onClick();
    }
    if (!item.disabled) {
      onSelect(item.key);
    }
  };

  return (
    <MotionBox
      as="li"
      role="menuitem"
      variants={menuItemVariants}
      {...menuItemHoverAnimation}
      initial="initial"
      animate="animate"
      exit="exit"
      onClick={handleClick}
      px={4}
      py={2}
      cursor={item.disabled ? 'not-allowed' : 'pointer'}
      opacity={item.disabled ? 0.5 : 1}
      bg={isSelected ? 'blackAlpha.50' : 'transparent'}
      _dark={{
        bg: isSelected ? 'whiteAlpha.100' : 'transparent',
      }}
      transition="all 0.2s"
      userSelect="none"
    >
      <HStack gap={2} color={item.disabled ? 'gray.400' : 'inherit'}>
        {item.icon && (
          <Box fontSize="1.1em" color={isSelected ? 'blue.500' : 'inherit'}>
            {item.icon}
          </Box>
        )}
        <Text flex="1" color={isSelected ? 'blue.500' : 'inherit'}>
          {item.label}
        </Text>
        {item.command && (
          <Text fontSize="sm" _dark={{ color: 'gray.400' }}>
            {item.command}
          </Text>
        )}
      </HStack>
    </MotionBox>
  );
};
