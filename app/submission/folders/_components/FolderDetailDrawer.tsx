'use client';

import React from 'react';
import {
  Drawer,
  Portal,
  Stack,
  Box,
  Text,
  Flex,
  Badge,
  Heading,
  Button,
  Separator,
} from '@chakra-ui/react';
import { AiOutlineFolderOpen, AiOutlineVideoCamera, AiOutlinePlus } from 'react-icons/ai';
import { Folder } from './types';
import VideoList from './VideoList';

// 组件 Props 类型
interface FolderDetailDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  folder: Folder | null;
  onCreateVideo?: () => void;
}

export default function FolderDetailDrawer({
  isOpen,
  onClose,
  folder,
  onCreateVideo,
}: FolderDetailDrawerProps) {
  if (!folder) return null;

  return (
    <Drawer.Root open={isOpen} onOpenChange={(e) => !e.open && onClose()} size="lg">
      <Portal>
        <Drawer.Backdrop />
        <Drawer.Positioner>
          <Drawer.Content>
            <Drawer.Header>
              <Flex align="center" gap={3}>
                <Box
                  p={2}
                  borderRadius="md"
                  bg={`${folder.color}.100`}
                  color={`${folder.color}.600`}
                >
                  <AiOutlineFolderOpen size={24} />
                </Box>
                <Box>
                  <Drawer.Title>{folder.name}</Drawer.Title>
                  <Badge
                    colorScheme={folder.color}
                    variant="subtle"
                    size="sm"
                  >
                    {folder.color === 'gray' ? '默认文件夹' : '自定义文件夹'}
                  </Badge>
                </Box>
              </Flex>
              <Drawer.CloseTrigger />
            </Drawer.Header>

            <Drawer.Body>
              <Stack gap={6}>
                {/* 文件夹信息 */}
                <Box>
                  <Heading size="sm" mb={3}>文件夹信息</Heading>
                  <Stack gap={3}>
                    <Box>
                      <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={1}>
                        描述
                      </Text>
                      <Text fontSize="sm" color="gray.600" lineHeight="1.5">
                        {folder.description}
                      </Text>
                    </Box>
                    
                    <Flex gap={6}>
                      <Box>
                        <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={1}>
                          创建时间
                        </Text>
                        <Text fontSize="sm" color="gray.600">
                          {new Date(folder.createdAt).toLocaleDateString('zh-CN')}
                        </Text>
                      </Box>
                      
                      {folder.updatedAt && (
                        <Box>
                          <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={1}>
                            更新时间
                          </Text>
                          <Text fontSize="sm" color="gray.600">
                            {new Date(folder.updatedAt).toLocaleDateString('zh-CN')}
                          </Text>
                        </Box>
                      )}
                    </Flex>

                    <Box>
                      <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={1}>
                        内容统计
                      </Text>
                      <Flex align="center" gap={1} fontSize="sm" color="gray.600">
                        <AiOutlineVideoCamera />
                        <Text>{folder.videoCount} 个视频</Text>
                      </Flex>
                    </Box>
                  </Stack>
                </Box>

                <Separator />

                {/* 视频列表 */}
                <Box>
                  <Flex justify="space-between" align="center" mb={4}>
                    <Heading size="sm">视频列表</Heading>
                    {onCreateVideo && (
                      <Button
                        size="sm"
                        leftIcon={<AiOutlinePlus />}
                        colorScheme="blue"
                        variant="outline"
                        onClick={onCreateVideo}
                      >
                        添加视频
                      </Button>
                    )}
                  </Flex>
                  
                  <VideoList folderId={folder.id} />
                </Box>
              </Stack>
            </Drawer.Body>
          </Drawer.Content>
        </Drawer.Positioner>
      </Portal>
    </Drawer.Root>
  );
}
