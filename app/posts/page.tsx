import { Container, Box } from '@chakra-ui/react';
import Content from '../_components/content';
import LeftSide from './_components/left-side';
import RightSide from './_components/right-side';
import PostsContent from './_components/posts-content';

export default function Page() {
  return (
    <Content>
      <Container
        maxW="100%"
        fluid
        className="grid h-full grid-cols-[260px_1fr] gap-3"
      >
        <LeftSide />
        <Box className="grid h-full grid-cols-[minmax(600px,_1fr)_300px] gap-3 overflow-y-auto scroll-smooth">
          <PostsContent />
          <RightSide />
        </Box>
      </Container>
    </Content>
  );
}
