import { Container, Box } from '@chakra-ui/react';
import Content from '../_components/content';
import LeftSideBar from './_components/left-side';
import SteamHeader from '../_components/header';

export async function generateMetadata() {
  return {
    title: '极致创思聚合网站|个人中心',
    content: '视频,文章,scratch编程网站聚合',
    csp: "default-src 'self'; script-src 'self' 'unsafe-inline';",
  };
}

export default function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <Box
      w="100%"
      className="bg-background text-foreground font-noto flex min-h-screen flex-col"
    >
      <SteamHeader />
      <Content>
        <Container
          maxW={'100%'}
          fluid
          px={0}
          position={'relative'}
          className="h-full md:grid md:grid-cols-4 md:gap-3"
        >
          <Box className="scroll-hover-hide invisible overflow-y-auto scroll-smooth !pr-3 md:visible md:col-span-1">
            <LeftSideBar />
          </Box>
          <Box className="absolute top-0 left-0 h-full w-full overflow-y-auto scroll-smooth md:relative md:col-span-3">
            {children}
          </Box>
        </Container>
      </Content>
    </Box>
  );
}
