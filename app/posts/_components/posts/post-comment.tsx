'use client';
import { memo } from 'react';
import { createPortal } from 'react-dom';
import { Box, Collapsible, IconButton } from '@chakra-ui/react';
import { AiOutlineComment } from 'react-icons/ai';
import dynamic from 'next/dynamic';

const CommentExample = dynamic(
  () => import('@/components/ui/comments/CommentExample'),
  {
    ssr: false,
  }
);

interface IProps {
  id: number;
  comments: number;
}

const PostCommentButton = ({ id, comments }: IProps) => {
  /**
   * @description 评论组件
   */
  return (
    <Collapsible.Root>
      <Collapsible.Trigger paddingY="3" asChild>
        <IconButton variant="ghost" px={2}>
          <AiOutlineComment />
          {comments}
        </IconButton>
      </Collapsible.Trigger>

      {typeof window !== 'undefined' &&
      document.getElementById(`comment-preview-${id}`)
        ? createPortal(
            <Collapsible.Content>
              <Box mt={2}>
                <CommentExample />
              </Box>
            </Collapsible.Content>,
            document.getElementById(`comment-preview-${id}`) as HTMLElement
          )
        : null}
    </Collapsible.Root>
  );
};
export default memo(PostCommentButton);
