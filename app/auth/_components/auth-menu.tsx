import { Tabs, Box } from '@chakra-ui/react';
import { AiOutlineWechat, AiOutlinePhone } from 'react-icons/ai';
import dynamic from 'next/dynamic';

const QRCodeView = dynamic(() => import('./qrcode-view'));
const CodeView = dynamic(() => import('./code-view'));

export default function AuthMenu() {
  return (
    <Box className="mx-auto !mt-12">
      <Tabs.Root defaultValue="wechat" fitted variant={'plain'}>
        <Tabs.List rounded="l3" p="1">
          <Tabs.Trigger value="wechat">
            <AiOutlineWechat />
            微信登录
          </Tabs.Trigger>
          <Tabs.Trigger value="phone">
            <AiOutlinePhone />
            验证码登录
          </Tabs.Trigger>
          <Tabs.Indicator rounded="l2" />
        </Tabs.List>
        <Tabs.Content py={6} value="wechat">
          <QRCodeView />
        </Tabs.Content>
        <Tabs.Content py={6} value="phone">
          <CodeView />
        </Tabs.Content>
      </Tabs.Root>
    </Box>
  );
}
