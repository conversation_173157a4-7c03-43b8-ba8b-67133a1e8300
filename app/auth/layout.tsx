import { Box, Grid, GridItem } from '@chakra-ui/react';
import LeftSide from './_components/left-side';
export async function generateMetadata() {
  return {
    title: '极致创思聚合网站|登录页面',
    content: '视频,文章,scratch编程网站聚合',
    csp: "default-src 'self'; script-src 'self' 'unsafe-inline';",
  };
}

export default function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <Box className="bg-background font-noto h-screen w-full overflow-hidden">
      <Grid className="h-full w-full" templateColumns="repeat(3, 1fr)">
        <GridItem
          colSpan={1}
          className="bg-background bg-[linear-gradient(0deg,rgba(var(--palette-background-defaultChannel)/92%),rgba(var(--palette-background-defaultChannel)/92%))] bg-[url('/images/auth-background.webp')] bg-cover bg-center"
        >
          <LeftSide />
        </GridItem>
        <GridItem colSpan={2} className="bg-background">
          {children}
        </GridItem>
      </Grid>
    </Box>
  );
}
