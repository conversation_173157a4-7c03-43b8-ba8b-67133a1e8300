'use client';

import { memo } from 'react';
import { createPortal } from 'react-dom';
import { IconButton, FileUpload } from '@chakra-ui/react';
import { AiOutlineVideoCamera } from 'react-icons/ai';

const PostsVideo = () => {
  /**
   * @description 视频上传组件
   */
  return (
    <FileUpload.Root accept="video/*" maxFiles={1}>
      <FileUpload.HiddenInput />
      <FileUpload.Trigger asChild>
        <IconButton bg="white" variant="ghost" size="sm" aria-label="添加视频">
          <AiOutlineVideoCamera />
        </IconButton>
      </FileUpload.Trigger>
      {/* 图片预览区域 */}
      {typeof window !== 'undefined' && document.getElementById('image-preview')
        ? createPortal(
            <FileUpload.ItemGroup>
              <FileUpload.Context>
                {({ acceptedFiles }) =>
                  acceptedFiles.map((file) => (
                    <FileUpload.Item key={file.name} file={file}>
                      <FileUpload.ItemPreview />
                      <FileUpload.ItemName />
                      <FileUpload.ItemSizeText />
                      <FileUpload.ItemDeleteTrigger />
                    </FileUpload.Item>
                  ))
                }
              </FileUpload.Context>
            </FileUpload.ItemGroup>,
            document.getElementById('image-preview') as Element
          )
        : null}
    </FileUpload.Root>
  );
};
export default memo(PostsVideo);
