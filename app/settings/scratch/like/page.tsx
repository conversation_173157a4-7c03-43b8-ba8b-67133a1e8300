import { Container, Flex } from '@chakra-ui/react';
import SettingsBreadcrumb from '@/app/settings/_components/settings-breadcrumb';

import ArticleCard from '../../_components/settings-card';

export default function Page() {
  /**
   * @description 用户收藏的文章
   */
  return (
    <Container maxW="6xl" h={'100%'}>
      <Flex direction={'column'} gap={6} p={4}>
        <SettingsBreadcrumb title="点赞的文章" />
        <Container maxW="100%" className="flex flex-col gap-4 rounded-md p-4">
          <ArticleCard />
        </Container>
      </Flex>
    </Container>
  );
}
