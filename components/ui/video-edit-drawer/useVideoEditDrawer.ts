import { useState, useCallback } from 'react';
import type { UseVideoEditDrawer } from './types';

/**
 * 视频编辑抽屉状态管理 Hook
 * 
 * @returns {UseVideoEditDrawer} 包含抽屉状态和控制方法的对象
 */
export const useVideoEditDrawer = (): UseVideoEditDrawer => {
  const [isOpen, setIsOpen] = useState(false);

  const open = useCallback(() => {
    setIsOpen(true);
  }, []);

  const close = useCallback(() => {
    setIsOpen(false);
  }, []);

  const toggle = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  return {
    isOpen,
    open,
    close,
    toggle,
  };
};
