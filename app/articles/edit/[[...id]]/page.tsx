import { Box } from '@chakra-ui/react';
import dynamic from 'next/dynamic';
import Content from '@/app/_components/content';

const ArticleMdEditor = dynamic(() => import('./_components/article-editor'));
const ArticleDrawer = dynamic(() => import('./_components/article-drawer'));

export default function Page() {
  return (
    <Content>
      <Box h={'100%'} className="bg-background">
        <ArticleDrawer />
        <ArticleMdEditor />
      </Box>
    </Content>
  );
}
