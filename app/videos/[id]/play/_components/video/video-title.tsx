import { Box, Card, Flex, Heading, Stack, Badge, Text } from '@chakra-ui/react';
import { AiFillEye, AiOutlineComment, AiTwotoneStop } from 'react-icons/ai';

export default function VideoTitle() {
  return (
    <Box>
      <Card.Root borderWidth={'0px'}>
        <Card.Body px="0">
          <Flex direction="column" gap="4">
            <Heading truncate>
              “他们就像游戏里隐藏的剧本线，隐晦且美好。”
            </Heading>
            <Stack direction={'row'} gap="4">
              <Badge variant="plain" px="0" size="md" color="gray.500">
                <AiFillEye />
                5.2k
              </Badge>
              <Badge variant="plain" px="0" size="md" color="gray.500">
                <AiOutlineComment />
                5.2k
              </Badge>
              <Text textStyle="md" className="!text-gray-500 dark:!text-white">
                2025-05-20 18:31:42
              </Text>
              <Badge variant="plain" px="0" color="red" size="md">
                <AiTwotoneStop />
                未经许可,请勿转载
              </Badge>
            </Stack>
          </Flex>
        </Card.Body>
      </Card.Root>
    </Box>
  );
}
