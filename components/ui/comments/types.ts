export interface Author {
  id: string;
  name: string;
  avatar: string;
}

export interface Comment {
  id: string;
  content: string;
  author: Author;
  createdAt: string;
  likes: number;
  replies?: Comment[];
  parentId?: string;
}

export interface CommentInputProps {
  onSubmit: (content: string) => void;
  placeholder?: string;
  isLoading?: boolean;
}

export interface CommentListProps {
  comments: Comment[];
  onLoadMore: () => void;
  hasMore: boolean;
  isLoading?: boolean;
}

export interface CommentItemProps {
  comment: Comment;
  onReply: (commentId: string, content: string) => void;
  onLike: (commentId: string) => void;
  onEdit: (commentId: string, content: string) => void;
  onDelete: (commentId: string) => void;
  currentUser?: Author;
}

export interface CommentsProps {
  comments: Comment[];
  currentUser?: Author;
  onAddComment: (content: string) => void;
  onReply: (commentId: string, content: string) => void;
  onLike: (commentId: string) => void;
  onEdit: (commentId: string, content: string) => void;
  onDelete: (commentId: string) => void;
  onLoadMore: () => void;
  hasMore: boolean;
  isLoading?: boolean;
}
