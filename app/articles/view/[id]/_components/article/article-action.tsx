'use client';
import { Card, IconButton, Stack } from '@chakra-ui/react';
import { AiFillLike, AiFillStar } from 'react-icons/ai';
import { IoMdShareAlt } from 'react-icons/io';

/**
 * 文章操作组件 - 包含点赞、收藏和分享功能的卡片式操作栏
 *
 * 使用Card组件作为容器，内部通过Stack布局水平排列三个操作按钮：
 * 1. 点赞按钮（使用AiFillLike图标）
 * 2. 收藏按钮（使用AiFillStar图标）
 * 3. 分享按钮（使用IoMdShareAlt图标）
 *
 * 所有按钮均采用plain变体的IconButton组件
 */
export default function ArticleAction() {
  return (
    <Card.Root className="rounded-lg shadow-md">
      <Card.Body className="flex w-full">
        <Stack direction={'row'} className="flex justify-around">
          <IconButton variant={'plain'}>
            <AiFillLike />
            点赞
          </IconButton>
          <IconButton variant={'plain'}>
            <AiFillStar />
            收藏
          </IconButton>
          <IconButton variant={'plain'}>
            <IoMdShareAlt />
            分享
          </IconButton>
        </Stack>
      </Card.Body>
    </Card.Root>
  );
}
