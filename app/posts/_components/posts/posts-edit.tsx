'use client';
import { useState, useCallback } from 'react';
import { Card, Textarea, VStack, Group, Flex, Button } from '@chakra-ui/react';
import dynamic from 'next/dynamic';

const PostsEmoji = dynamic(() => import('./posts-emoji'), { ssr: false });
const PostsImage = dynamic(() => import('./posts-image'), { ssr: false });
const PostsVideo = dynamic(() => import('./posts-video'), { ssr: false });

/**
 * 帖子编辑组件
 *
 * 提供一个带有文本输入框和多媒体操作按钮的卡片式编辑界面，
 * 用户可输入文本内容并添加图片、视频或表情符号。
 *
 * @returns 返回包含编辑表单的React组件
 */
export default function PostsEdit() {
  const [value, setValue] = useState('');
  const [uploadType, setUploadType] = useState<'image' | 'video' | null>(null);

  const handleEmojiSelect = useCallback((emoji: unknown) => {
    if (
      typeof emoji === 'object' &&
      emoji !== null &&
      'native' in emoji &&
      typeof (emoji as any).native === 'string'
    ) {
      setValue((prev) => prev + (emoji as { native: string }).native);
    }
  }, []);

  const handleValueChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setValue(e.target.value);
  };
  return (
    <Card.Root
      className="shadow-md"
      borderRadius="md"
      width="100%"
      variant="subtle"
    >
      <Card.Body>
        <form>
          <VStack gap="4" align="stretch">
            <Textarea
              resize="none"
              rows={5}
              variant="subtle"
              placeholder="有什么新鲜事想分享给大家吗？"
              size="md"
              value={value}
              onChange={handleValueChange}
              css={{ '--focus-color': 'colors.primary' }}
            />
            <Flex justify="space-between" align="center">
              <Group gap="2">
                <PostsImage />
                <PostsVideo />
                {/* 添加表情组件 */}
                <PostsEmoji handleEmojiSelect={handleEmojiSelect} />
              </Group>
              <Button size="sm" bg="primary" color="white" colorScheme="blue">
                发布
              </Button>
            </Flex>
            <div
              id="image-preview"
              className="grid h-auto w-full grid-cols-3 gap-2"
            ></div>
          </VStack>
        </form>
      </Card.Body>
    </Card.Root>
  );
}
