'use client';
import { memo } from 'react';
import { Stack, Flex, Group, IconButton } from '@chakra-ui/react';
import { AiOutlineRead, AiOutlineDelete } from 'react-icons/ai';
import { Tooltip } from '@/components/ui/tooltip';

import MessageCard from '@/app/settings/message/_components/message-card';

const NotificationContainer = () => {
  return (
    <Stack direction={'column'} gap={4}>
      <Flex justify={'flex-end'} bg="gray.50">
        <Group>
          <Tooltip
            showArrow
            content="全部已读"
            positioning={{ placement: 'top' }}
          >
            <IconButton size={'sm'} variant="plain">
              <AiOutlineRead />
            </IconButton>
          </Tooltip>

          <Tooltip
            showArrow
            content="清空消息"
            positioning={{ placement: 'top' }}
          >
            <IconButton colorPalette={'red'} size={'sm'} variant="plain">
              <AiOutlineDelete />
            </IconButton>
          </Tooltip>
        </Group>
      </Flex>

      <Stack direction={'column'} gap={4}>
        <MessageCard />
      </Stack>
    </Stack>
  );
};
export default memo(NotificationContainer);
