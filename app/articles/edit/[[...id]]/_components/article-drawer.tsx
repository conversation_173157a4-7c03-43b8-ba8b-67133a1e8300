'use client';
import { useActionState } from 'react';
import { <PERSON><PERSON>, <PERSON>B<PERSON><PERSON>, Drawer, Portal } from '@chakra-ui/react';
import { useArticleCreateStore } from '@/store/article-create-store';

import ArticleMenu from './article-menu';
import { createArticle } from '@/app/actions/article-actions';
import type { InitialValueState } from '@/app/articles/types';

/**
 * 文章抽屉组件 - 用于创建/编辑文章的抽屉式弹窗
 *
 * 使用zustand状态管理抽屉的显示/隐藏状态
 * 包含标题区、内容区（ArticleMenu组件）和底部操作按钮区
 * 提供取消和发布两个操作按钮，点击关闭按钮可关闭抽屉
 */
export default function ArticleDrawer() {
  const drawerShow = useArticleCreateStore((state) => state.drawerShow);
  const handleDrawerShow = useArticleCreateStore(
    (state) => state.handleDrawerShow
  );

  const initialValue: InitialValueState = {
    categories: '',
    title: '',
    tags: [],
    content: '',
  };
  const [state, dispatch, isPending] = useActionState(
    createArticle,
    initialValue
  );

  const onSubmit = (data: InitialValueState) => {
    console.log('data', data);
  };

  return (
    <Drawer.Root
      open={drawerShow}
      size="md"
      onOpenChange={(e) => handleDrawerShow(e.open)}
    >
      <Portal>
        <Drawer.Backdrop />
        <Drawer.Positioner>
          <Drawer.Content>
            <Drawer.Header>
              <Drawer.Title>发布文章</Drawer.Title>
            </Drawer.Header>
            <Drawer.Body>
              <ArticleMenu initialValue={initialValue} onSubmit={onSubmit} />
            </Drawer.Body>
            <Drawer.Footer>
              <Button variant="outline" onClick={() => handleDrawerShow()}>
                取消
              </Button>
              <Button type="submit" form="article-form">
                确认并发布
              </Button>
            </Drawer.Footer>
            <Drawer.CloseTrigger asChild>
              <CloseButton size="sm" />
            </Drawer.CloseTrigger>
          </Drawer.Content>
        </Drawer.Positioner>
      </Portal>
    </Drawer.Root>
  );
}
