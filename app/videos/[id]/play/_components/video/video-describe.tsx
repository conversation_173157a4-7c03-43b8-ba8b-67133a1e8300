'use client';
import { useState } from 'react';
import { Box, Flex, Text, Wrap, Tag } from '@chakra-ui/react';

export default function VideoDescribe() {
  const [count, setCount] = useState(20);
  return (
    <Box py={4} borderBottom={'2px solid #eee'}>
      <Flex direction="column" gap="4">
        <Text
          textStyle={'md'}
          className="!text-gray-500 dark:!text-white"
          lineClamp={2}
        >
          视频描述
        </Text>
        <Wrap gap="2">
          {Array.from({ length: count }).map((_, index) => (
            <Tag.Root variant="subtle" colorPalette={'gray.500'} key={index}>
              <Tag.Label>Tag here</Tag.Label>
            </Tag.Root>
          ))}
        </Wrap>
      </Flex>
    </Box>
  );
}
