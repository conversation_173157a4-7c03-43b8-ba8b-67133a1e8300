'use client';
import {
  Card,
  Box,
  AspectRatio,
  Image,
  Center,
  Stack,
  Avatar,
  Heading,
  Text,
} from '@chakra-ui/react';
import NextImage from 'next/image';

import Paint from '@/public/images/paint.svg';

/**
 * 用户卡片组件
 *
 * 展示用户信息的卡片式UI组件，包含用户头像、名称和简短描述
 *
 * @returns 返回一个包含用户信息的卡片组件，包含顶部背景图、居中头像和底部文字信息
 *
 * 组件特性：
 * - 使用16:7比例的顶部背景图
 * - 圆形头像居中显示并有白色边框效果
 * - 用户名称和描述文字居中显示
 * - 整体采用卡片式阴影设计
 */
export default function UserCard() {
  return (
    <Card.Root className="shadow-md">
      <Card.Body p={0}>
        <Box className="flex flex-col">
          <AspectRatio ratio={16 / 7} className="relative">
            <Image asChild>
              <NextImage
                src={Paint}
                alt="paint"
                layout="cover"
                objectFit="contain"
                className="rounded-md"
              />
            </Image>
          </AspectRatio>

          <Stack gap={2}>
            <Center>
              <Box
                bg={'white'}
                className="outline-2 outline-offset-4 outline-white"
              >
                <Avatar.Root size={'2xl'} className="-translate-y-5">
                  <Avatar.Fallback name="Segun Adebayo" />
                  <Avatar.Image src="https://bit.ly/sage-adebayo" />
                </Avatar.Root>
              </Box>
            </Center>
            <Box mb={3}>
              <Heading size="md" textAlign={'center'}>
                Segun Adebayo
              </Heading>
              <Text
                lineClamp={2}
                className="text-foreground text-center text-sm"
              >
                人员描述
              </Text>
            </Box>
          </Stack>
        </Box>
      </Card.Body>
    </Card.Root>
  );
}
