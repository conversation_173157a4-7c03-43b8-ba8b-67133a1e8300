{"name": "steam_polymerization_web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@bytemd/plugin-gfm": "^1.22.0", "@bytemd/plugin-highlight": "^1.22.0", "@bytemd/react": "^1.22.0", "@chakra-ui/next-js": "^2.4.2", "@chakra-ui/react": "^3.21.0", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.1.1", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.3.3", "@types/mdx": "^2.0.13", "ahooks": "^3.8.5", "chakra-react-select": "^6.1.0", "emoji-mart": "^5.6.0", "framer-motion": "^12.18.1", "github-markdown-css": "^5.8.1", "next": "15.3.3", "next-mdx-remote-client": "^2.1.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-icons": "^5.5.0", "react-player": "^2.16.0", "react-resizable-panels": "^3.0.3", "react-use": "^17.6.0", "yup": "^1.6.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/eslint-plugin-next": "^15.3.3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.28.0", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-you-might-not-need-an-effect": "^0.1.4", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4", "typescript": "^5"}}