import { Container, Heading, Flex, Text } from '@chakra-ui/react';

export async function generateMetadata() {
  return {
    title: '极致创思聚合网站|消息中心',
    content: '视频,文章,scratch编程网站聚合',
    csp: "default-src 'self'; script-src 'self' 'unsafe-inline';",
  };
}

export default function Layout({
  children,
  drawer,
}: Readonly<{
  children: React.ReactNode;
  drawer: React.ReactNode;
}>) {
  return (
    <Container maxW="6xl" h={'100%'} py={6}>
      {drawer}
      <Heading size={'lg'} as={'h2'}>
        消息中心
      </Heading>
      <Text className="mt-2" color="fg.muted">
        这里是您已读的消息列表。
      </Text>
      <Flex direction={'column'} gap={4} mt={4}>
        {children}
      </Flex>
    </Container>
  );
}
