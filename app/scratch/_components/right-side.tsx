import { Box, Flex, IconButton } from '@chakra-ui/react';
import { AiOutlineRocket } from 'react-icons/ai';
import dynamic from 'next/dynamic';

const ScratchRecommend = dynamic(() => import('./scratch-recommend'));

export default function RightSide() {
  return (
    <Box>
      <Flex direction="column" gap={6}>
        <ScratchRecommend />
        <IconButton bg="primary" color="white">
          <AiOutlineRocket />
          创建scratch作品
        </IconButton>
      </Flex>
    </Box>
  );
}
