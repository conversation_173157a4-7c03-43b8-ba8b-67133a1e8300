'use client';

import React from 'react';
import {
  Dialog,
  Portal,
  Stack,
  Button,
  Box,
  Text,
  Flex,
  Image,
  AspectRatio,
  Alert,
} from '@chakra-ui/react';
import { AiOutlinePlayCircle, AiOutlineWarning } from 'react-icons/ai';
import { Video } from './types';

// 组件 Props 类型
interface VideoDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  video: Video | null;
  onConfirm: (videoId: string) => void;
}

// 格式化时长
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  const mb = bytes / (1024 * 1024);
  return `${mb.toFixed(1)} MB`;
};

export default function VideoDeleteDialog({
  isOpen,
  onClose,
  video,
  onConfirm,
}: VideoDeleteDialogProps) {
  // 处理确认删除
  const handleConfirm = () => {
    if (video) {
      onConfirm(video.id);
    }
  };

  if (!video) return null;

  return (
    <Dialog.Root open={isOpen} onOpenChange={(e) => !e.open && onClose()}>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content maxW="md">
            <Dialog.Header>
              <Dialog.Title>删除视频</Dialog.Title>
              <Dialog.CloseTrigger />
            </Dialog.Header>

            <Dialog.Body>
              <Stack gap={4}>
                {/* 视频信息 */}
                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={3}>
                    要删除的视频
                  </Text>
                  <Box
                    p={4}
                    borderRadius="md"
                    border="1px solid"
                    borderColor="gray.200"
                    bg="gray.50"
                  >
                    <Flex gap={3}>
                      <Box width="100px" flexShrink={0} position="relative">
                        <AspectRatio ratio={16 / 9}>
                          <Image
                            src={video.coverImage}
                            alt={video.title}
                            borderRadius="md"
                            objectFit="cover"
                          />
                        </AspectRatio>
                        <Flex
                          position="absolute"
                          bottom={1}
                          right={1}
                          bg="blackAlpha.700"
                          color="white"
                          px={1}
                          py={0.5}
                          borderRadius="sm"
                          fontSize="xs"
                          align="center"
                          gap={1}
                        >
                          <AiOutlinePlayCircle size={10} />
                          <Text>{formatDuration(video.duration)}</Text>
                        </Flex>
                      </Box>

                      <Box flex={1}>
                        <Text fontWeight="medium" fontSize="sm" mb={1}>
                          {video.title}
                        </Text>
                        <Text
                          fontSize="xs"
                          color="gray.600"
                          noOfLines={2}
                          mb={2}
                        >
                          {video.description}
                        </Text>
                        <Stack gap={1} fontSize="xs" color="gray.500">
                          <Text>大小: {formatFileSize(video.size)}</Text>
                          <Text>
                            创建:{' '}
                            {new Date(video.createdAt).toLocaleDateString(
                              'zh-CN'
                            )}
                          </Text>
                        </Stack>
                      </Box>
                    </Flex>
                  </Box>
                </Box>

                {/* 警告信息 */}
                <Alert.Root status="warning">
                  <Alert.Indicator>
                    <AiOutlineWarning />
                  </Alert.Indicator>
                  <Alert.Title>注意：此操作无法撤销</Alert.Title>
                  <Alert.Description>
                    删除视频后，视频文件和所有相关数据将被永久删除，无法恢复。
                  </Alert.Description>
                </Alert.Root>

                {/* 确认文本 */}
                <Box>
                  <Text fontSize="sm" color="gray.700">
                    您确定要删除视频{' '}
                    <Text as="span" fontWeight="bold">
                      "{video.title}"
                    </Text>{' '}
                    吗？
                  </Text>
                  <Text fontSize="sm" color="gray.600" mt={2}>
                    此操作将永久删除视频文件，请谨慎操作。
                  </Text>
                </Box>
              </Stack>
            </Dialog.Body>

            <Dialog.Footer>
              <Stack direction="row" gap={3} width="100%">
                <Button variant="outline" onClick={onClose} flex={1}>
                  取消
                </Button>
                <Button colorScheme="red" onClick={handleConfirm} flex={1}>
                  确认删除
                </Button>
              </Stack>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
}
