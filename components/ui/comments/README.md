# 评论组件 (Comments Component)

一个功能完整的React评论组件，基于Chakra UI构建，支持评论、回复、点赞、编辑和删除等功能。

## 特性

- 🚀 默认显示评论列表，支持分页加载更多
- 💬 支持嵌套回复（多层评论）
- 👍 支持评论点赞
- ✏️ 支持编辑和删除评论（仅评论作者可见）
- 📱 响应式设计，适配移动端和桌面端
- 🌓 支持明暗主题切换
- 🔒 基于用户权限的操作控制
- 📝 完整的TypeScript类型支持

## 安装

确保已安装以下依赖：

```bash
npm install @chakra-ui/react @emotion/react @emotion/styled framer-motion react-icons
```

## 基本使用

```tsx
import { Comments, useComments } from '@/components/ui/comments';

const YourComponent = () => {
  const {
    comments,
    hasMore,
    isLoading,
    loadMore,
    addComment,
    addReply,
    likeComment,
    editComment,
    deleteComment,
  } = useComments({
    initialComments: [], // 初始评论数据
    pageSize: 5, // 每页显示数量
    fetchComments: async (page, pageSize) => {
      // 实现获取评论的逻辑
      const response = await fetch(
        `/api/comments?page=${page}&pageSize=${pageSize}`
      );
      return response.json();
    },
  });

  const currentUser = {
    id: 'user-1',
    name: '用户名',
    avatar: '头像URL',
  };

  return (
    <Comments
      comments={comments}
      currentUser={currentUser}
      onAddComment={(content) => addComment(content, currentUser)}
      onReply={(commentId, content) =>
        addReply(commentId, content, currentUser)
      }
      onLike={likeComment}
      onEdit={editComment}
      onDelete={deleteComment}
      onLoadMore={loadMore}
      hasMore={hasMore}
      isLoading={isLoading}
    />
  );
};
```

## 组件结构

```
comments/
├── Comments.tsx         # 主评论组件
├── CommentList.tsx      # 评论列表组件
├── CommentItem.tsx      # 单条评论组件
├── CommentInput.tsx     # 评论输入框组件
├── types.ts             # 类型定义
├── hooks/
│   └── useComments.ts   # 评论相关hooks
├── mockData.ts          # 模拟数据（用于示例）
├── CommentExample.tsx   # 示例组件
└── index.ts             # 导出文件
```

## API参考

### Comments 组件

```typescript
interface CommentsProps {
  comments: Comment[]; // 评论数据
  currentUser?: Author; // 当前用户信息
  onAddComment: (content: string) => void; // 添加评论回调
  onReply: (commentId: string, content: string) => void; // 回复评论回调
  onLike: (commentId: string) => void; // 点赞回调
  onEdit: (commentId: string, content: string) => void; // 编辑评论回调
  onDelete: (commentId: string) => void; // 删除评论回调
  onLoadMore: () => void; // 加载更多回调
  hasMore: boolean; // 是否还有更多评论
  isLoading?: boolean; // 加载状态
}
```

### useComments Hook

```typescript
interface UseCommentsProps {
  initialComments?: Comment[]; // 初始评论数据
  pageSize?: number; // 每页显示数量
  fetchComments?: (page: number, pageSize: number) => Promise<Comment[]>; // 获取评论的异步函数
}

const {
  comments, // 评论列表
  hasMore, // 是否还有更多评论
  isLoading, // 加载状态
  loadMore, // 加载更多函数
  addComment, // 添加评论函数
  addReply, // 添加回复函数
  likeComment, // 点赞函数
  editComment, // 编辑评论函数
  deleteComment, // 删除评论函数
} = useComments(props);
```

### 数据类型

```typescript
interface Author {
  id: string;
  name: string;
  avatar: string;
}

interface Comment {
  id: string;
  content: string;
  author: Author;
  createdAt: string;
  likes: number;
  replies?: Comment[];
  parentId?: string;
}
```

## 示例

查看 `CommentExample.tsx` 文件获取完整示例。

## 自定义样式

组件使用Chakra UI构建，支持通过Chakra UI的主题系统进行样式定制：

```tsx
import { ChakraProvider, extendTheme } from '@chakra-ui/react';

const theme = extendTheme({
  components: {
    // 自定义组件样式
  },
});

const App = () => (
  <ChakraProvider theme={theme}>
    <YourComponent />
  </ChakraProvider>
);
```

## 注意事项

1. 需要提供currentUser才能启用评论功能
2. 编辑和删除功能仅对评论作者可见
3. 评论支持无限嵌套，但建议控制嵌套层级（建议不超过3层）
4. 默认每页加载5条评论，可通过pageSize属性调整

## 贡献

欢迎提交问题和改进建议！
