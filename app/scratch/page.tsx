import { Container } from '@chakra-ui/react';
import Content from '../_components/content';
import LeftSide from './_components/left-side';
import RightSide from './_components/right-side';

export default function Page() {
  return (
    <Content>
      <Container
        maxW="100%"
        fluid
        className="grid h-full grid-cols-[minmax(600px,_1fr)_300px] gap-3 overflow-hidden overflow-x-auto scroll-smooth"
      >
        <LeftSide />
        <RightSide />
      </Container>
    </Content>
  );
}
