'use client';
import {
  Card,
  Image,
  Heading,
  Text,
  Avatar,
  H<PERSON>tack,
  Separator,
  Badge,
} from '@chakra-ui/react';
import NextImage from 'next/image';
import { AiFillEye, AiFillHeart } from 'react-icons/ai';
import { motion } from 'framer-motion';
import Paint from '@/public/images/paint.svg';

// 创建 motion 版本的组件
const MotionCard = motion(Card.Root);
const MotionImage = motion(Image);
const MotionHeading = motion(Heading);

export default function ArticleCard() {
  return (
    <MotionCard
      width="100%"
      minH="150px"
      maxH="170px"
      variant="outline"
      cursor={'pointer'}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{
        scale: 1.02,
        transition: { duration: 0.2 },
      }}
    >
      <Card.Body>
        <div className="group grid w-full grid-cols-4 gap-3">
          <div className="col-span-3 flex flex-col justify-between">
            <MotionHeading
              size="xl"
              className="group-hover:!text-primary"
              whileHover={{ scale: 1.01 }}
              transition={{ duration: 0.2 }}
            >
              文章标题
            </MotionHeading>
            <Text lineClamp="2" className="!text-md !text-foreground">
              文章描述
            </Text>
            <motion.div
              className="!mb-2 flex justify-between"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <div className="flex gap-3">
                <HStack>
                  <Avatar.Root size="xs">
                    <Avatar.Fallback name="Segun Adebayo" />
                    <Avatar.Image src="https://bit.ly/sage-adebayo" />
                  </Avatar.Root>
                  <Heading size="sm">作者姓名</Heading>
                </HStack>
                <Separator orientation="vertical" />
                <Badge variant="plain" px="0">
                  <AiFillEye />
                  5.2k
                </Badge>
                <Separator orientation="vertical" />
                <Badge variant="plain" px="0">
                  <AiFillHeart />
                  37
                </Badge>
              </div>

              <HStack>
                <Badge variant="outline">人工智能</Badge>
                <Badge variant="outline">LLM</Badge>
              </HStack>
            </motion.div>
          </div>
          <div className="col-span-1 justify-self-end">
            <MotionImage
              asChild
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <NextImage src={Paint} alt="card" width={140} height={130} />
            </MotionImage>
          </div>
        </div>
      </Card.Body>
    </MotionCard>
  );
}
