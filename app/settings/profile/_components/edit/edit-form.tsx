'use client';
import { Box, Field, Input, Button } from '@chakra-ui/react';
import dynamic from 'next/dynamic';
import { useForm, Controller } from 'react-hook-form';

const options = [
  { value: 'man', label: '男' },
  { value: 'woman', label: '女' },
  { value: 'other', label: '其他' },
];

const CreatableSelect = dynamic(
  () => import('chakra-react-select').then((mod) => mod.CreatableSelect),
  { ssr: false }
);

export default function EditForm() {
  const { control, handleSubmit } = useForm();

  const onSubmit = (data) => {
    console.log(data);
  };

  return (
    <form>
      <Box className="grid grid-cols-2 gap-4">
        <Field.Root>
          <Field.Label>姓名</Field.Label>
          <Input variant={'outline'} size={'md'} placeholder="John Doe" />
        </Field.Root>

        <Field.Root>
          <Field.Label>邮箱</Field.Label>
          <Input variant={'outline'} size={'md'} placeholder="<EMAIL>" />
        </Field.Root>

        <Field.Root disabled>
          <Field.Label>手机号</Field.Label>
          <Input variant={'outline'} size={'md'} placeholder="1234567890" />
        </Field.Root>

        <Field.Root>
          <Field.Label>地址</Field.Label>
          <Input variant={'outline'} size={'md'} placeholder="123 Main St" />
        </Field.Root>

        <Field.Root>
          <Field.Label>性别</Field.Label>
          <Controller
            name="gender"
            control={control}
            render={({ field }) => (
              <CreatableSelect
                tagVariant="outline"
                options={options}
                name={field.name}
                value={field.value}
                onChange={field.onChange}
                getOptionLabel={(option) => option.label}
                getOptionValue={(option) => option.value}
                closeMenuOnSelect={false}
                chakraStyles={{
                  dropdownIndicator: (provided) => ({
                    ...provided,
                    bg: 'transparent',
                    px: 2,
                    cursor: 'inherit',
                  }),
                  indicatorSeparator: (provided) => ({
                    ...provided,
                    display: 'none',
                  }),
                }}
              />
            )}
          />
        </Field.Root>

        <Box className="col-span-2">
          <Box className="col-start-2 justify-self-end">
            <Button variant={'subtle'}>提交</Button>
          </Box>
        </Box>
      </Box>
    </form>
  );
}
