'use client';

import {
  VS<PERSON>ck,
  Button,
  Icon,
  Box,
  useBreakpointValue,
  Link as ChakraLink,
} from '@chakra-ui/react';
import { RiTimeLine } from 'react-icons/ri';
import { AiOutlineFire, AiOutlineHeart } from 'react-icons/ai';
import { usePathname } from 'next/navigation';
import { IconType } from 'react-icons';
import Link from 'next/link';

interface NavItem {
  label: string;
  icon: IconType;
  href: string;
}

const navItems: NavItem[] = [
  { label: '最新', icon: RiTimeLine, href: '/posts/latest' },
  { label: '热门', icon: AiOutlineFire, href: '/posts/hot' },
  { label: '关注', icon: AiOutlineHeart, href: '/posts/following' },
];

const LeftSide = () => {
  const pathname = usePathname();
  const showLabels = useBreakpointValue({ base: false, md: true });

  return (
    <Box
      as="nav"
      position="sticky"
      top="4"
      py="4"
      minW={{ base: '60px', md: '200px' }}
    >
      <VStack gap="2" align="stretch">
        {navItems.map((item) => {
          const isActive = pathname === item.href;
          return (
            <ChakraLink
              key={item.href}
              as={Link}
              href={item.href}
              _hover={{ textDecoration: 'none' }}
              display="block"
            >
              <Button
                w="full"
                variant="ghost"
                justifyContent="flex-start"
                h="40px"
                px="4"
                bg={isActive ? 'accent.subtle' : 'transparent'}
                color={isActive ? 'accent.default' : 'text.primary'}
                _hover={{
                  bg: isActive ? 'accent.muted' : 'bg.subtle',
                }}
                _active={{
                  bg: isActive ? 'accent.muted' : 'bg.muted',
                }}
                transition="all 0.2s"
                fontSize="sm"
              >
                <Icon
                  as={item.icon}
                  boxSize="4"
                  mr={showLabels ? '2' : '0'}
                  color={isActive ? 'accent.default' : 'text.secondary'}
                />
                {showLabels && item.label}
              </Button>
            </ChakraLink>
          );
        })}
      </VStack>
    </Box>
  );
};

export default LeftSide;
