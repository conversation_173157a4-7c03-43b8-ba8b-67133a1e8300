import { createSystem, defaultConfig, defineConfig } from '@chakra-ui/react';

const config = defineConfig({
  cssVarsRoot: ':where(:root, :host)',
  // cssVarsPrefix: 'steam',
  preflight: false,
  theme: {
    breakpoints: {
      xs: '0',
      sm: '600px',
      md: '900px',
      lg: '1200px',
      xl: '1536px',
    },
    tokens: {
      colors: {
        primary: { value: 'var(--color-blue-400)' },
        secondary: { value: 'var(--color-violet-400)' },

        // 语义化颜色令牌 - 背景
        bg: {
          default: { value: 'var(--color-bg)' },
          subtle: { value: 'var(--color-bg-subtle)' },
          muted: { value: 'var(--color-bg-muted)' },
          emphasized: { value: 'var(--color-bg-emphasized)' },
        },

        // 语义化颜色令牌 - 文本
        text: {
          default: { value: 'var(--color-text)' },
          muted: { value: 'var(--color-text-muted)' },
          subtle: { value: 'var(--color-text-subtle)' },
          emphasized: { value: 'var(--color-text-emphasized)' },
        },

        // 语义化颜色令牌 - 边框
        border: {
          default: { value: 'var(--color-border)' },
          subtle: { value: 'var(--color-border-subtle)' },
          emphasized: { value: 'var(--color-border-emphasized)' },
        },

        // 语义化颜色令牌 - 卡片
        card: {
          bg: { value: 'var(--color-card-bg)' },
          border: { value: 'var(--color-card-border)' },
        },

        // 语义化颜色令牌 - 按钮
        button: {
          bg: { value: 'var(--color-button-bg)' },
          hover: { value: 'var(--color-button-hover)' },
          active: { value: 'var(--color-button-active)' },
        },
      },
      fonts: {
        body: { value: 'var(--font-noto-sans-sc)' },
      },
      spacing: {
        gutter: { value: '8px' },
      },
    },
  },
});

const system = createSystem(defaultConfig, config);
export default system;
