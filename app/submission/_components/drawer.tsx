'use client';
import { useState } from 'react';

import {
  VideoEditDrawer,
  useVideoEditDrawer,
  type VideoData,
  type VideoFormData,
  type FolderOption,
} from '@/components/ui/video-edit-drawer';
import { toaster } from '@/components/ui/toaster';

const sampleVideo: VideoData = {
  id: '1',
  title: '我的测试视频',
  description: '这是一个用于测试的视频描述',
  coverImage:
    'https://via.placeholder.com/320x180/4299e1/ffffff?text=Test+Video',
  folderId: 'default',
};

const sampleFolders: FolderOption[] = [
  { value: 'default', label: '默认文件夹' },
  { value: 'education', label: '教育' },
  { value: 'entertainment', label: '娱乐' },
  { value: 'technology', label: '科技' },
];

export default function Drawer() {
  const [currentVideo, setCurrentVideo] = useState<VideoData>(sampleVideo);
  const { isOpen, open, close } = useVideoEditDrawer();

  const handleSave = (data: VideoFormData) => {
    console.log('保存数据:', data);

    // 更新当前视频数据
    setCurrentVideo((prev) => ({
      ...prev,
      ...data,
      coverImage:
        typeof data.coverImage === 'string' ? data.coverImage : prev.coverImage,
      updatedAt: new Date().toISOString(),
    }));

    // 显示成功提示
    toaster.create({
      title: '保存成功',
      description: '视频信息已保存',
      type: 'success',
      duration: 3000,
    });

    close();
  };

  // 处理发布
  const handlePublish = (data: VideoFormData) => {
    console.log('发布数据:', data);

    // 更新当前视频数据
    setCurrentVideo((prev) => ({
      ...prev,
      ...data,
      coverImage:
        typeof data.coverImage === 'string' ? data.coverImage : prev.coverImage,
      updatedAt: new Date().toISOString(),
    }));

    // 显示成功提示
    toaster.create({
      title: '发布成功',
      description: '视频已成功发布',
      type: 'success',
      duration: 3000,
    });

    close();
  };
  return (
    <VideoEditDrawer
      isOpen={isOpen}
      onClose={close}
      videoData={currentVideo}
      folders={sampleFolders}
      onSave={handleSave}
      onPublish={handlePublish}
    />
  );
}
