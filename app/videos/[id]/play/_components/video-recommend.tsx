'use client';
import { useState } from 'react';
import { Box, Flex, Heading } from '@chakra-ui/react';
import VideoCard from './card/video-card';

export default function VideoRecommend() {
  /**
   * @description 推荐的作品列表
   */
  const [count, setCount] = useState(10);
  return (
    <Box className="flex flex-col" gap={2}>
      <Heading>推荐的作品</Heading>
      <Flex direction="column" gap={'6'}>
        {Array.from({ length: count }).map((_, index) => (
          <VideoCard key={index} />
        ))}
      </Flex>
    </Box>
  );
}
