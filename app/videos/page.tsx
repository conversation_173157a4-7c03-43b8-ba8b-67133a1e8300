import dynamic from 'next/dynamic';
import { Container } from '@chakra-ui/react';
import Content from '../_components/content';

const TopSide = dynamic(() => import('./_components/top-side'));
const VideoContent = dynamic(() => import('./_components/video-content'));

export default function Page() {
  return (
    <Content>
      <Container
        maxW="100%"
        fluid
        className="flex h-full flex-col gap-3 overflow-y-scroll scroll-smooth"
      >
        <TopSide />
        <VideoContent />
      </Container>
    </Content>
  );
}
