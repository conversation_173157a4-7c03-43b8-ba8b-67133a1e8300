'use client';
import { useRouter } from 'next/navigation';
import { MdEmail, MdSettings, MdInfo, MdLock } from 'react-icons/md';
import { SmoothMenu } from '@/components/ui/smooth-menu';
import { MenuItem } from '@/components/ui/smooth-menu/types';
import {
  AiOutlineRead,
  AiTwotoneVideoCamera,
  AiOutlineReddit,
} from 'react-icons/ai';

const LeftSideBar = () => {
  const router = useRouter();

  // 菜单项配置
  const items: MenuItem[] = [
    {
      key: '1',
      icon: <MdEmail />,
      label: '消息中心',
      children: [{ key: '11', label: '消息列表' }],
    },
    {
      key: '2',
      icon: <MdSettings />,
      label: '系统设置',
      children: [
        { key: '21', label: '个人资料', icon: <MdInfo /> },
        { key: '22', label: '账户安全', icon: <MdLock /> },
        // {
        //   key: '23',
        //   label: '通知设置',
        //   icon: <MdNotifications />,
        //   children: [
        //     { key: '231', label: '邮件通知' },
        //     { key: '232', label: '短信通知' },
        //     { key: '233', label: '应用通知' },
        //   ],
        // },
        // {
        //   key: '24',
        //   label: '显示设置',
        //   icon: <MdVisibility />,
        //   children: [
        //     { key: '241', label: '主题设置' },
        //     { key: '242', label: '字体大小' },
        //     { key: '243', label: '布局设置' },
        //   ],
        // },
      ],
    },
    {
      key: '3',
      icon: <AiOutlineRead />,
      label: '文章',
      children: [
        { key: '31', label: '我点赞的' },
        { key: '32', label: '我的收藏' },
        { key: '33', label: '最近访问' },
      ],
    },
    {
      key: '4',
      icon: <AiTwotoneVideoCamera />,
      label: '视频',
      children: [
        { key: '41', label: '我点赞的' },
        { key: '42', label: '我的收藏' },
        { key: '43', label: '最近访问' },
      ],
    },
    {
      key: '5',
      icon: <AiOutlineReddit />,
      label: 'scratch编程',
      children: [
        { key: '50', label: '我的作品' },
        { key: '51', label: '我点赞的' },
        { key: '52', label: '最近访问' },
      ],
    },
  ];

  const handleSelect = (key: string) => {
    // 根据选中的菜单项执行不同的操作
    switch (key) {
      case '11':
        router.push('/settings/message');
        break;
      case '21':
        router.push('/settings/profile');
        break;
      case '22':
        router.push('/settings/security');
        break;

      case '31':
        router.push('/settings/articles/favor');
        break;
      case '32':
        router.push('/settings/articles/like');
        break;
      case '33':
        router.push('/settings/articles/history');
        break;

      case '41':
        router.push('/settings/videos/like');
        break;
      case '42':
        router.push('/settings/videos/favor');
        break;
      case '43':
        router.push('/settings/videos/history');
        break;
      case '50':
        router.push('/settings/scratch/personal');
        break;
      case '51':
        router.push('/settings/scratch/like');
        break;
      case '52':
        router.push('/settings/scratch/history');
        break;
      default:
        console.log(`Selected key: ${key}`);
    }
  };

  return (
    <SmoothMenu
      items={items}
      defaultSelectedKeys={['2']}
      defaultOpenKeys={['21']}
      onSelect={handleSelect}
    />
  );
};
export default LeftSideBar;
