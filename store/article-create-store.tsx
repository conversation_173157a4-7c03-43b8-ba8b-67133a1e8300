import { create } from 'zustand';

interface ArticleCreateState {
  show: boolean; // 控制发布文章按钮显示和隐藏
  handleShow: () => void;
  drawerShow: boolean; // 控制抽屉显示和隐藏
  handleDrawerShow: (state?: boolean) => void;
}

export const useArticleCreateStore = create<ArticleCreateState>((set) => ({
  show: false, // default false
  handleShow: () => set((state) => ({ show: !state.show })),
  drawerShow: false, // default false
  handleDrawerShow: (state?: boolean) =>
    set((prev) => ({
      drawerShow: state !== undefined ? state : !prev.drawerShow,
    })),
}));
