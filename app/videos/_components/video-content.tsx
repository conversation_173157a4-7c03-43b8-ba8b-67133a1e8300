'use client';
import { useState } from 'react';
import { Box } from '@chakra-ui/react';
import VideoCard from './card/video-card';

export default function VideoContent() {
  const [count, setCount] = useState(20);
  return (
    <Box className="flex flex-wrap gap-3">
      {Array.from({ length: count }).map((_, index) => (
        <Box key={index}>
          <VideoCard />
        </Box>
      ))}
    </Box>
  );
}
