import { Box, Center, Container } from '@chakra-ui/react';
import Content from '@/app/_components/content';
import LeftSide from './_components/left-side';
import RightSide from './_components/right-side';

export default function Page() {
  return (
    <Content>
      <Container
        maxW="100%"
        fluid
        className="flex h-full flex-col gap-6 overflow-y-auto scroll-smooth"
      >
        <Box h="100%">
          <Center>
            <Container
              maxW="6xl"
              fluid
              className="bg-background grid grid-cols-3 gap-4 rounded-md"
            >
              <LeftSide />
              <RightSide />
            </Container>
          </Center>
        </Box>
      </Container>
    </Content>
  );
}
